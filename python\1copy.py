from flask import Flask, request, jsonify
import pulp

app = Flask(__name__)

@app.route('/solve', methods=['POST'])
def solve_lp():
    # 从请求 body 获取 JSON 数据
    data = request.get_json()

    # 提取 profit_coeff 和 constraints
    profit_coeff = data.get('profit_coeff', {})
    constraints = data.get('constraints', {})

    # 验证输入数据
    if not profit_coeff or not constraints:
        return jsonify({"error": "缺少 profit_coeff 或 constraints"}), 400

    # 初始化线性规划问题
    prob = pulp.LpProblem("Steel_Production_Sensitivity", pulp.LpMaximize)

    # 动态创建决策变量
    variables = {name: pulp.LpVariable(name, lowBound=0, upBound=100000) for name in profit_coeff.keys()}

    # 设置目标函数
    prob += pulp.lpSum(variables[name] * coeff for name, coeff in profit_coeff.items())

    # 设置约束条件
    for constraint_name, constraint_data in constraints.items():
        if len(constraint_data) != len(variables) + 1:
            return jsonify({"error": f"{constraint_name} 的约束数据格式错误"}), 400
        coeff_list = constraint_data[:-1]  # 系数列表
        rhs = constraint_data[-1]          # 右侧值
        prob += (pulp.lpSum(variables[name] * coeff for name, coeff in zip(variables.keys(), coeff_list)) <= rhs, constraint_name)

    # 求解问题
    prob.solve(pulp.PULP_CBC_CMD())

    # 检查是否成功求解
    if prob.status != pulp.LpStatusOptimal:
        return jsonify({"error": "未找到最优解"}), 400

    # 收集变量的灵敏度分析数据
    variable_data = []
    for name, var in variables.items():
        # var_info = {
        #     "变量名": name,
        #     "终值": var.varValue,
        #     "递减成本": var.dj,
        #     "目标式系数": profit_coeff[name],
        #     "允许增量": var.upBound - var.varValue if var.upBound else float('inf'),
        #     "允许减量": var.varValue - var.lowBound
        # }
        var_info = {
            "变量名": name,
            "终值": var.varValue,
            "递减成本": var.dj,
            "目标式系数": profit_coeff[name],
            "允许增量": (var.upBound - var.varValue) if var.upBound is not None else float('inf'),
            "允许减量": (var.varValue - var.lowBound) if var.lowBound is not None else var.varValue
        }
        variable_data.append(var_info)

    # 收集约束的灵敏度分析数据
    constraint_data = []
    for constraint_name in constraints.keys():
        constraint = prob.constraints[constraint_name]
        slack = constraint.slack
        rhs = constraints[constraint_name][-1]
        constraint_info = {
            "约束名称": constraint_name,
            "终值": rhs - slack,
            "影子价格": constraint.pi,
            "约束限制值": rhs,
            "允许增量": float('inf') if constraint.pi == 0 else (constraint.pi * (rhs + 1) - constraint.pi * rhs),
            "允许减量": float('inf') if constraint.pi == 0 else (constraint.pi * rhs - constraint.pi * (rhs - 1))
        }
        constraint_data.append(constraint_info)

    # 构造 JSON 响应
    response = {
        "variable_data": variable_data,
        "constraint_data": constraint_data
    }

    return jsonify(response)

if __name__ == '__main__':
    app.run(debug=True)