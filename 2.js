const slover = require("javascript-lp-solver");

function optimizeSteelProduction(params) {
  // 参数解包
  const { prices, costs, resourceLimits, consumptionMatrix } = params;

  // 创建模型
  const model = {
    optimize: "profit",
    opType: "max",
    constraints: {},
    variables: {},
    ints: {},
  };

  // 添加变量
  model.variables = {
    X1: { profit: prices[0] },
    X2: { profit: prices[1] },
    X3: { profit: prices[2] },
    Y1: { profit: -costs[0] },
    Y2: { profit: -costs[1] },
    Y3: { profit: -costs[2] },
    Y4: { profit: -costs[3] },
  };

  // 设置整数变量
  model.ints = { Y1: 1, Y2: 1, Y3: 1, Y4: 1 };

  // 添加资源约束
  ["limestone", "coke", "coal", "scrap"].forEach((res, i) => {
    model.constraints[res] = { max: resourceLimits[i] };
    model.variables.X1[res] = consumptionMatrix[i][0];
    model.variables.X2[res] = consumptionMatrix[i][1];
    model.variables.X3[res] = consumptionMatrix[i][2];
  });

  // 添加碳排放约束
  model.constraints.emission = { max: 63000 };
  model.variables.X1.emission = consumptionMatrix[4][0];
  model.variables.X2.emission = consumptionMatrix[4][1];
  model.variables.X3.emission = consumptionMatrix[4][2];

  // 求解
  const results = slover.Solve(model);

  return {
    production: [results.X1, results.X2, results.X3],
    upgrades: [results.Y1, results.Y2, results.Y3, results.Y4],
    profit: results.result,
  };
}

// 示例调用
const params = {
  prices: [5000, 8000, 12000], // 钢铁单价
  costs: [200000, 150000, 300000, 250000], // 升级成本
  resourceLimits: [20000, 15000, 10000, 8000], // 资源限制
  consumptionMatrix: [
    [0.8, 1.2, 1.5], // 石灰石
    [0.5, 0.7, 0.9], // 焦炭
    [0.3, 0.4, 0.6], // 煤粉
    [0.2, 0.3, 0.4], // 废钢
    [2.5, 3.0, 3.8], // 碳排放
  ],
};

const solution = optimizeSteelProduction(params);
console.log("最优解:", solution);
