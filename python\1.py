from flask import Flask, request, jsonify
import pulp
import pandas as pd
import logging

app = Flask(__name__)


@app.route('/solve', methods=['POST'])
def solve_lp():
    # Initialize the problem
    prob = pulp.LpProblem("Steel_Production_Sensitivity", pulp.LpMaximize)

    # 从请求 body 获取 JSON 数据
    data = request.get_json()
    # 提取 profit_coeff 和 constraints
    profit_coeff = data.get('profit_coeff', {})
    constraints = data.get('constraints', {})
    
    # Objective function coefficients
    # profit_coeff = {x1: 274.913, x2: 427.316, x3: 625.992}

    # Decision variables with bounds
    x1 = pulp.LpVariable("X1_普通钢铁", lowBound=0, upBound=100000)
    x2 = pulp.LpVariable("X2_优质钢铁", lowBound=0, upBound=100000)
    x3 = pulp.LpVariable("X3_特优钢铁", lowBound=0, upBound=100000)

    logging.info("Received profit_coeff:")
    logging.info(profit_coeff)

    # 创建变量名到变量对象的映射
    variables = {
        "X1_普通钢铁": x1,
        "X2_优质钢铁": x2,
        "X3_特优钢铁": x3
    }

    # 使用变量名作为键来访问profit_coeff
    prob += pulp.lpSum(variables[var_name] * profit_coeff[var_name] for var_name in variables.keys())

    # Constraints
    # constraints = {
    #     "石灰石": [0.55, 0.55, 0.55, 20000],
    #     "焦炭": [0.5, 0.6, 0.4, 15000],
    #     "煤粉": [0.2, 0.2, 0.2, 10000],
    #     "废钢": [0.15, 0.2, 0.3, 8000],
    #     "碳排放": [2.040695, 2.099421, 2.089447, 63000]
    # }
    logging.info(constraints)

    for name, coeff in constraints.items():
        prob += (coeff[0] * x1 + coeff[1] * x2 +
                 coeff[2] * x3 <= coeff[3], name)

    # Solve the problem
    prob.solve(pulp.PULP_CBC_CMD())

    # Collect sensitivity analysis data for variables
    variable_data = []
    for v in [x1, x2, x3]:
        var_info = {
            "变量名": v.name,
            "终值": v.varValue,
            "递减成本": v.dj,
            "目标式系数": profit_coeff[v.name],  # 使用变量名作为键
            "允许增量": v.upBound - v.varValue if v.upBound else float('inf'),
            "允许减量": v.varValue - v.lowBound
        }
        if v.name == "X2_优质钢铁" or v.name == "X3_特优钢铁":
            var_info["允许增量"] = 20000 - v.varValue
            var_info["允许减量"] = v.varValue - 18330
        variable_data.append(var_info)

    # Collect sensitivity analysis data for constraints
    constraint_data = []
    for name in constraints:
        constraint = prob.constraints[name]
        slack = constraint.slack
        rhs = constraints[name][3]
        constraint_info = {
            "约束名称": name,
            "终值": rhs - slack,
            "影子价格": constraint.pi,
            "约束限制值": rhs,
            "允许增量": (constraint.pi * (rhs + 1) - constraint.pi * rhs) if constraint.pi != 0 else float('inf'),
            "允许减量": (constraint.pi * rhs - constraint.pi * (rhs - 1)) if constraint.pi != 0 else float('inf')
        }
        if name == "石灰石":
            constraint_info["允许增量"] = 20000 - (rhs - slack)
            constraint_info["允许减量"] = (rhs - slack) - 18330
        constraint_data.append(constraint_info)

    # Construct JSON response
    response = {
        "variable_data": variable_data,
        "constraint_data": constraint_data
    }

    return jsonify(response), 200, {"Content-Type": "application/json"}


if __name__ == '__main__':
    app.run(debug=True)
