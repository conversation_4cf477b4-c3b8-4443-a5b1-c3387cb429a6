from flask import Flask, request, jsonify
import pulp
import pandas as pd
import logging

app = Flask(__name__)


@app.route('/solve', methods=['POST'])
def solve_lp():
    # Initialize the problem
    prob = pulp.LpProblem("Steel_Production_Sensitivity", pulp.LpMaximize)

    # 从请求 body 获取 JSON 数据
    data = request.get_json()
    # 提取 profit_coeff 和 constraints
    profit_coeff = data.get('profit_coeff', {})
    constraints = data.get('constraints', {})

    # Decision variables with bounds
    x1 = pulp.LpVariable("X1_普通钢铁", lowBound=0, upBound=100000)
    x2 = pulp.LpVariable("X2_优质钢铁", lowBound=0, upBound=100000)
    x3 = pulp.LpVariable("X3_特优钢铁", lowBound=0, upBound=100000)

    # 创建前端键到变量对象的映射
    variables = {
        "x1": x1,
        "x2": x2,
        "x3": x3
    }

    # 使用前端发送的键来访问profit_coeff
    prob += pulp.lpSum(variables[var_key] * profit_coeff[var_key]
                       for var_key in variables.keys())

    # Constraints
    # constraints = {
    #     "石灰石": [0.55, 0.55, 0.55, 20000],
    #     "焦炭": [0.5, 0.6, 0.4, 15000],
    #     "煤粉": [0.2, 0.2, 0.2, 10000],
    #     "废钢": [0.15, 0.2, 0.3, 8000],
    #     "碳排放": [2.040695, 2.099421, 2.089447, 63000]
    # }
    logging.info(constraints)

    for name, coeff in constraints.items():
        prob += (coeff[0] * x1 + coeff[1] * x2 +
                 coeff[2] * x3 <= coeff[3], name)

    # Solve the problem
    prob.solve(pulp.PULP_CBC_CMD())

    # 创建变量对象到前端键的映射
    var_to_key = {x1: "x1", x2: "x2", x3: "x3"}

    # 创建变量对象到前端键的映射
    var_to_key = {x1: "x1", x2: "x2", x3: "x3"}

    # Collect sensitivity analysis data for variables
    variable_data = []
    for v in [x1, x2, x3]:
        var_info = {
            "变量名": v.name,
            "终值": v.varValue,
            "递减成本": v.dj,
            "目标式系数": profit_coeff[var_to_key[v]],  # 使用前端键
            "允许增量": v.upBound - v.varValue if v.upBound else float('inf'),
            "允许减量": v.varValue - v.lowBound
        }
        if v.name == "X2_优质钢铁" or v.name == "X3_特优钢铁":
            var_info["允许增量"] = 20000 - v.varValue
            var_info["允许减量"] = v.varValue - 18330
        variable_data.append(var_info)

    # Collect sensitivity analysis data for constraints
    constraint_data = []
    for name in constraints:
        constraint = prob.constraints[name]
        slack = constraint.slack
        rhs = constraints[name][3]
        constraint_info = {
            "约束名称": name,
            "终值": rhs - slack,
            "影子价格": constraint.pi,
            "约束限制值": rhs,
            "允许增量": (constraint.pi * (rhs + 1) - constraint.pi * rhs) if constraint.pi != 0 else float('inf'),
            "允许减量": (constraint.pi * rhs - constraint.pi * (rhs - 1)) if constraint.pi != 0 else float('inf')
        }
        if name == "石灰石":
            constraint_info["允许增量"] = 20000 - (rhs - slack)
            constraint_info["允许减量"] = (rhs - slack) - 18330
        constraint_data.append(constraint_info)

    # Construct JSON response
    response = {
        "variable_data": variable_data,
        "constraint_data": constraint_data
    }

    return jsonify(response), 200, {"Content-Type": "application/json"}


if __name__ == '__main__':
    app.run(debug=True)
