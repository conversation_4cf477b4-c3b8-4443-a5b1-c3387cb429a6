{"name": "Fancy Stock Cutting Problem", "optimize": "count", "opType": "min", "_timeout": 5000, "options": {"tolerance": 3, "timeout": 1500}, "variables": {"version0": {"cut11": 8, "cut21": 0, "cut84": 0, "cut3.5": 0, "cut79.5": 0, "count": 1}, "version1": {"cut11": 7, "cut21": 0, "cut84": 0, "cut3.5": 3, "cut79.5": 0, "count": 1}, "version2": {"cut11": 6, "cut21": 1, "cut84": 0, "cut3.5": 0, "cut79.5": 0, "count": 1}, "version3": {"cut11": 6, "cut21": 0, "cut84": 0, "cut3.5": 6, "cut79.5": 0, "count": 1}, "version4": {"cut11": 5, "cut21": 1, "cut84": 0, "cut3.5": 3, "cut79.5": 0, "count": 1}, "version5": {"cut11": 5, "cut21": 0, "cut84": 0, "cut3.5": 9, "cut79.5": 0, "count": 1}, "version6": {"cut11": 4, "cut21": 2, "cut84": 0, "cut3.5": 0, "cut79.5": 0, "count": 1}, "version7": {"cut11": 4, "cut21": 1, "cut84": 0, "cut3.5": 6, "cut79.5": 0, "count": 1}, "version8": {"cut11": 4, "cut21": 0, "cut84": 0, "cut3.5": 12, "cut79.5": 0, "count": 1}, "version9": {"cut11": 3, "cut21": 3, "cut84": 0, "cut3.5": 0, "cut79.5": 0, "count": 1}, "version10": {"cut11": 3, "cut21": 2, "cut84": 0, "cut3.5": 3, "cut79.5": 0, "count": 1}, "version11": {"cut11": 3, "cut21": 1, "cut84": 0, "cut3.5": 9, "cut79.5": 0, "count": 1}, "version12": {"cut11": 3, "cut21": 0, "cut84": 0, "cut3.5": 15, "cut79.5": 0, "count": 1}, "version13": {"cut11": 2, "cut21": 3, "cut84": 0, "cut3.5": 1, "cut79.5": 0, "count": 1}, "version14": {"cut11": 2, "cut21": 2, "cut84": 0, "cut3.5": 7, "cut79.5": 0, "count": 1}, "version15": {"cut11": 2, "cut21": 1, "cut84": 0, "cut3.5": 13, "cut79.5": 0, "count": 1}, "version16": {"cut11": 2, "cut21": 0, "cut84": 0, "cut3.5": 19, "cut79.5": 0, "count": 1}, "version17": {"cut11": 1, "cut21": 4, "cut84": 0, "cut3.5": 0, "cut79.5": 0, "count": 1}, "version18": {"cut11": 1, "cut21": 3, "cut84": 0, "cut3.5": 4, "cut79.5": 0, "count": 1}, "version19": {"cut11": 1, "cut21": 2, "cut84": 0, "cut3.5": 10, "cut79.5": 0, "count": 1}, "version20": {"cut11": 1, "cut21": 1, "cut84": 0, "cut3.5": 16, "cut79.5": 0, "count": 1}, "version21": {"cut11": 1, "cut21": 0, "cut84": 1, "cut3.5": 0, "cut79.5": 0, "count": 1}, "version22": {"cut11": 1, "cut21": 0, "cut84": 0, "cut3.5": 22, "cut79.5": 0, "count": 1}, "version23": {"cut11": 1, "cut21": 0, "cut84": 0, "cut3.5": 1, "cut79.5": 1, "count": 1}, "version24": {"cut11": 0, "cut21": 4, "cut84": 0, "cut3.5": 1, "cut79.5": 0, "count": 1}, "version25": {"cut11": 0, "cut21": 3, "cut84": 0, "cut3.5": 7, "cut79.5": 0, "count": 1}, "version26": {"cut11": 0, "cut21": 2, "cut84": 0, "cut3.5": 13, "cut79.5": 0, "count": 1}, "version27": {"cut11": 0, "cut21": 1, "cut84": 0, "cut3.5": 19, "cut79.5": 0, "count": 1}, "version28": {"cut11": 0, "cut21": 0, "cut84": 1, "cut3.5": 1, "cut79.5": 0, "count": 1}, "version29": {"cut11": 0, "cut21": 0, "cut84": 0, "cut3.5": 25, "cut79.5": 0, "count": 1}, "version30": {"cut11": 0, "cut21": 0, "cut84": 0, "cut3.5": 4, "cut79.5": 1, "count": 1}}, "ints": {"version0": 1, "version1": 1, "version2": 1, "version3": 1, "version4": 1, "version5": 1, "version6": 1, "version7": 1, "version8": 1, "version9": 1, "version10": 1, "version11": 1, "version12": 1, "version13": 1, "version14": 1, "version15": 1, "version16": 1, "version17": 1, "version18": 1, "version19": 1, "version20": 1, "version21": 1, "version22": 1, "version23": 1, "version24": 1, "version25": 1, "version26": 1, "version27": 1, "version28": 1, "version29": 1, "version30": 1}, "constraints": {"cut11": {"min": 28}, "cut21": {"min": 14}, "cut84": {"min": 8}, "cut3.5": {"min": 42}, "cut79.5": {"min": 4}}, "expects": {"feasible": true, "result": 19, "version30": 2, "version9": 4, "version21": 2, "version28": 6, "version15": 2, "version23": 2, "version0": 1}}