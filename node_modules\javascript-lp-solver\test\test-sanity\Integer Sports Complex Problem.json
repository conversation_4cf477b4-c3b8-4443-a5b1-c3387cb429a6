{"name": "Integer Sports Complex Problem", "optimize": "usage", "opType": "max", "constraints": {"cost": {"max": 120000}, "land": {"max": 12}, "pools": {"max": 1}, "tennisCourts": {"max": 1}, "fields": {"max": 1}, "gyms": {"max": 1}}, "variables": {"pool": {"land": 4, "cost": 35000, "usage": 300, "pools": 1}, "tennisCourt": {"land": 2, "cost": 10000, "usage": 90, "tennisCourts": 1}, "field": {"land": 7, "cost": 25000, "usage": 400, "fields": 1}, "gym": {"land": 3, "cost": 90000, "usage": 150, "gyms": 1}}, "ints": {"pool": 1, "tennisCourt": 1, "field": 1, "gym": 1}, "expects": {"feasible": true, "pool": 1, "tennisCourt": 0, "field": 1, "gym": 0, "result": 700}}