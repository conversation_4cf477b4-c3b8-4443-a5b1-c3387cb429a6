#!/usr/bin/env python3
"""
测试 Infinity 值的处理
"""
import json

# 测试 handle_infinity 函数
def handle_infinity(value):
    if value == float('inf') or value == float('-inf'):
        return "Infinity" if value > 0 else "-Infinity"
    return value

# 测试数据
test_values = [
    123.45,
    float('inf'),
    float('-inf'),
    0,
    -123.45
]

print("测试 handle_infinity 函数:")
for value in test_values:
    result = handle_infinity(value)
    print(f"  {value} -> {result} (类型: {type(result).__name__})")

# 测试 JSON 序列化
test_data = {
    "normal_value": 123.45,
    "positive_infinity": handle_infinity(float('inf')),
    "negative_infinity": handle_infinity(float('-inf')),
    "zero": handle_infinity(0)
}

print("\n测试 JSON 序列化:")
try:
    json_str = json.dumps(test_data, indent=2)
    print("✅ JSON 序列化成功:")
    print(json_str)
    
    # 测试反序列化
    parsed = json.loads(json_str)
    print("\n✅ JSON 反序列化成功:")
    for key, value in parsed.items():
        print(f"  {key}: {value} (类型: {type(value).__name__})")
        
except Exception as e:
    print(f"❌ JSON 处理失败: {e}")

print("\n✅ Infinity 处理测试完成！")
