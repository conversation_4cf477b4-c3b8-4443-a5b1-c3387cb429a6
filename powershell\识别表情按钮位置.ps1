# PowerShell 脚本：调用 Python 进行图像识别
$pythonScript = @"
import cv2
import numpy as np
import pyautogui
import sys

# 设置 pyautogui 的安全暂停
pyautogui.FAILSAFE = True
pyautogui.PAUSE = 0.1

# 加载模板图像（要查找的图片）
template = cv2.imread('./images/emoji_wc.png', cv2.IMREAD_GRAYSCALE)
if template is None:
    print("Error: 无法加载模板图像")
    sys.exit(1)

# 捕获屏幕截图
screenshot = pyautogui.screenshot()
screen = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2GRAY)

# 模板匹配
result = cv2.matchTemplate(screen, template, cv2.TM_CCOEFF_NORMED)
min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

# 设置匹配阈值
threshold = 0.8
if max_val >= threshold:
    top_left = max_loc
    h, w = template.shape
    center_x = top_left[0] + w // 2
    center_y = top_left[1] + h // 2
    print(f"找到匹配: X={center_x}, Y={center_y}")
else:
    print("未找到匹配")
    sys.exit(1)
"@

# 将 Python 脚本保存到临时文件
$pythonScriptPath = "$env:TEMP\image_recognition.py"
$pythonScript | Out-File -FilePath $pythonScriptPath -Encoding UTF8

# 执行 Python 脚本
$pythonExe = "python"  # 确保系统中已安装 Python
$result = & $pythonExe $pythonScriptPath

# 输出结果
Write-Host $result

# 清理临时文件
Remove-Item $pythonScriptPath