{"name": "Knapsack 1", "background": "https://developers.google.com/optimization/bin/knapsack#python", "optimize": "profit", "opType": "max", "constraints": {"capacity": {"max": 850}}, "variables": {"x0": {"capacity": 7, "profit": 360}, "x1": {"capacity": 0, "profit": 83}, "x2": {"capacity": 30, "profit": 59}, "x3": {"capacity": 22, "profit": 130}, "x4": {"capacity": 80, "profit": 431}, "x5": {"capacity": 94, "profit": 67}, "x6": {"capacity": 11, "profit": 230}, "x7": {"capacity": 81, "profit": 52}, "x8": {"capacity": 70, "profit": 93}, "x9": {"capacity": 64, "profit": 125}, "x10": {"capacity": 59, "profit": 670}, "x11": {"capacity": 18, "profit": 892}, "x12": {"capacity": 0, "profit": 600}, "x13": {"capacity": 36, "profit": 38}, "x14": {"capacity": 3, "profit": 48}, "x15": {"capacity": 8, "profit": 147}, "x16": {"capacity": 15, "profit": 78}, "x17": {"capacity": 42, "profit": 256}, "x18": {"capacity": 9, "profit": 63}, "x19": {"capacity": 0, "profit": 17}, "x20": {"capacity": 42, "profit": 120}, "x21": {"capacity": 47, "profit": 164}, "x22": {"capacity": 52, "profit": 432}, "x23": {"capacity": 32, "profit": 35}, "x24": {"capacity": 26, "profit": 92}, "x25": {"capacity": 48, "profit": 110}, "x26": {"capacity": 55, "profit": 22}, "x27": {"capacity": 6, "profit": 42}, "x28": {"capacity": 29, "profit": 50}, "x29": {"capacity": 84, "profit": 323}, "x30": {"capacity": 2, "profit": 514}, "x31": {"capacity": 4, "profit": 28}, "x32": {"capacity": 18, "profit": 87}, "x33": {"capacity": 56, "profit": 73}, "x34": {"capacity": 7, "profit": 78}, "x35": {"capacity": 29, "profit": 15}, "x36": {"capacity": 93, "profit": 26}, "x37": {"capacity": 44, "profit": 78}, "x38": {"capacity": 71, "profit": 210}, "x39": {"capacity": 3, "profit": 36}, "x40": {"capacity": 86, "profit": 85}, "x41": {"capacity": 66, "profit": 189}, "x42": {"capacity": 31, "profit": 274}, "x43": {"capacity": 65, "profit": 43}, "x44": {"capacity": 0, "profit": 33}, "x45": {"capacity": 79, "profit": 10}, "x46": {"capacity": 20, "profit": 19}, "x47": {"capacity": 65, "profit": 389}, "x48": {"capacity": 52, "profit": 276}, "x49": {"capacity": 13, "profit": 312}}, "binaries": {"x0": 1, "x1": 1, "x2": 1, "x3": 1, "x4": 1, "x5": 1, "x6": 1, "x7": 1, "x8": 1, "x9": 1, "x10": 1, "x11": 1, "x12": 1, "x13": 1, "x14": 1, "x15": 1, "x16": 1, "x17": 1, "x18": 1, "x19": 1, "x20": 1, "x21": 1, "x22": 1, "x23": 1, "x24": 1, "x25": 1, "x26": 1, "x27": 1, "x28": 1, "x29": 1, "x30": 1, "x31": 1, "x32": 1, "x33": 1, "x34": 1, "x35": 1, "x36": 1, "x37": 1, "x38": 1, "x39": 1, "x40": 1, "x41": 1, "x42": 1, "x43": 1, "x44": 1, "x45": 1, "x46": 1, "x47": 1, "x48": 1, "x49": 1}, "expects": {"feasible": true, "x0": 1, "x1": 1, "x3": 1, "x4": 1, "x6": 1, "x24": 1, "x10": 1, "x11": 1, "x12": 1, "x14": 1, "x15": 1, "x16": 1, "x17": 1, "x18": 1, "x19": 1, "x21": 1, "x22": 1, "x28": 1, "x27": 1, "x29": 1, "x30": 1, "x31": 1, "x32": 1, "x34": 1, "x38": 1, "x39": 1, "x41": 1, "x42": 1, "x44": 1, "x47": 1, "x48": 1, "x49": 1, "result": 7534}}