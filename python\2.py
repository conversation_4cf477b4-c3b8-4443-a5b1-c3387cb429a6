import pulp
import pandas as pd

def calculate_sensitivity():
    # 初始化问题
    prob = pulp.LpProblem("Steel_Production_Sensitivity", pulp.LpMaximize)

    # 决策变量，根据实际情况合理设置上下界，这里示例设置一个较大但合理的上界
    x1 = pulp.LpVariable("X1_普通钢铁", lowBound=0, upBound=100000)  
    x2 = pulp.LpVariable("X2_优质钢铁", lowBound=0, upBound=100000)
    x3 = pulp.LpVariable("X3_特优钢铁", lowBound=0, upBound=100000)

    # 目标函数系数（使用你实际的系数，这里先按示例后续可替换）
    profit_coeff = {x1: 500, x2: 800, x3: 1200}
    prob += pulp.lpSum(var * profit_coeff[var] for var in [x1, x2, x3])

    # 约束条件（使用你实际的消耗系数和限制值等，这里结合你说的最优解情况调整）
    constraints = {
        "石灰石": [2, 3, 4, 20000],
        "焦炭": [1, 2, 3, 15000],
        "煤粉": [1, 1, 2, 10000],
        "废钢": [0.5, 1, 1.5, 8000],
        "碳排放": [3, 5, 7, 63000]
    }

    for name, coeff in constraints.items():
        prob += (coeff[0] * x1 + coeff[1] * x2 + coeff[2] * x3 <= coeff[3], name)

    # 求解
    prob.solve(pulp.PULP_CBC_CMD())  # 使用 CBC 求解器，也可根据情况换其他

    # 敏感性分析数据收集 - 变量部分
    variable_data = []
    for v in [x1, x2, x3]:
        var_info = {
            "变量名": v.name,
            "终值": v.varValue,
            "递减成本": v.dj,
            "目标式系数": profit_coeff[v],
            # 先初始化为按上下界计算
            "允许增量": v.upBound - v.varValue if v.upBound else float('inf'),
            "允许减量": v.varValue - v.lowBound
        }
        # 进一步根据最优解稳定性，动态调整允许增量和减量（简单逻辑：当变量取到边界值时，对应方向变动受限）
        if v.varValue == v.upBound:
            var_info["允许增量"] = 0
        if v.varValue == v.lowBound:
            var_info["允许减量"] = 0
        variable_data.append(var_info)

    # 敏感性分析数据收集 - 约束部分
    constraint_data = []
    for name in constraints:
        constraint = prob.constraints[name]
        slack = constraint.slack
        rhs = constraints[name][3]
        shadow_price = constraint.pi

        # 计算允许增量：在影子价格不变的前提下，约束右端项可增加的量（基于对偶理论，简单通过试探法近似，更准确可结合对偶问题）
        allowable_increase = 0
        test_rhs = rhs
        while True:
            new_constraints = {n: c for n, c in constraints.items()}
            new_constraints[name] = new_constraints[name][:3] + [test_rhs + 1]
            new_prob = pulp.LpProblem("Temp_Problem", pulp.LpMaximize)
            new_x1 = pulp.LpVariable("X1_普通钢铁", lowBound=0, upBound=100000)  
            new_x2 = pulp.LpVariable("X2_优质钢铁", lowBound=0, upBound=100000)
            new_x3 = pulp.LpVariable("X3_特优钢铁", lowBound=0, upBound=100000)
            new_profit_coeff = {new_x1: 500, new_x2: 800, new_x3: 1200}
            new_prob += pulp.lpSum(var * new_profit_coeff[var] for var in [new_x1, new_x2, new_x3])
            for n, c in new_constraints.items():
                new_prob += (c[0] * new_x1 + c[1] * new_x2 + c[2] * new_x3 <= c[3], n)
            new_prob.solve(pulp.PULP_CBC_CMD())
            if new_prob.status != 1 or pulp.value(new_prob.objective) is None:
                break
            # 简单判断影子价格是否变化（这里通过目标函数变化率近似，更准确需对比对偶变量）
            obj_change = pulp.value(new_prob.objective) - pulp.value(prob.objective)
            if abs(obj_change - shadow_price) > 1e-6:  # 允许小误差
                break
            allowable_increase += 1
            test_rhs += 1

        # 计算允许减量：类似允许增量的逻辑
        allowable_decrease = 0
        test_rhs = rhs
        while True:
            new_constraints = {n: c for n, c in constraints.items()}
            new_constraints[name] = new_constraints[name][:3] + [test_rhs - 1]
            new_prob = pulp.LpProblem("Temp_Problem", pulp.LpMaximize)
            new_x1 = pulp.LpVariable("X1_普通钢铁", lowBound=0, upBound=100000)  
            new_x2 = pulp.LpVariable("X2_优质钢铁", lowBound=0, upBound=100000)
            new_x3 = pulp.LpVariable("X3_特优钢铁", lowBound=0, upBound=100000)
            new_profit_coeff = {new_x1: 500, new_x2: 800, new_x3: 1200}
            new_prob += pulp.lpSum(var * new_profit_coeff[var] for var in [new_x1, new_x2, new_x3])
            for n, c in new_constraints.items():
                new_prob += (c[0] * new_x1 + c[1] * new_x2 + c[2] * new_x3 <= c[3], n)
            new_prob.solve(pulp.PULP_CBC_CMD())
            if new_prob.status != 1 or pulp.value(new_prob.objective) is None:
                break
            obj_change = pulp.value(prob.objective) - pulp.value(new_prob.objective)
            if abs(obj_change - shadow_price) > 1e-6:  # 允许小误差
                break
            allowable_decrease += 1
            test_rhs -= 1

        constraint_info = {
            "约束名称": name,
            "终值": rhs - slack,
            "影子价格": shadow_price,
            "约束限制值": rhs,
            "允许增量": allowable_increase,
            "允许减量": allowable_decrease
        }
        constraint_data.append(constraint_info)

    # 输出结果
    print("=== 可变单元格分析 ===")
    print(pd.DataFrame(variable_data).to_markdown(index=False))

    print("\n=== 约束条件分析 ===")
    print(pd.DataFrame(constraint_data).to_markdown(index=False))

if __name__ == "__main__":
    calculate_sensitivity()

