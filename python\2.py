import pulpimport pandas as pd
def sensitivity_analysis():
    # ----------------------
    # 1. 基础数据与模型构建
    # ----------------------
    prob = pulp.LpProblem("Steel_Sensitivity", pulp.LpMaximize)
    
    # 决策变量
    x1 = pulp.LpVariable("普通钢铁", lowBound=0, upBound=50000)
    x2 = pulp.LpVariable("优质钢铁", lowBound=0, upBound=50000)
    x3 = pulp.LpVariable("特优钢铁", lowBound=0, upBound=50000)
    
    # 目标函数系数（根据灵敏区间反推）
    c1, c2, c3 = -274.892, -427.305, -625.971  # 与linprog保持一致
    prob += c1*x1 + c2*x2 + c3*x3, "总利润"
    
    # 约束条件
    constraints = {
        "石灰石": (2, 3, 4, 20000),
        "焦炭": (1, 2, 3, 15000),
        "煤粉": (1, 1, 2, 10000),
        "废钢": (0.5, 1, 1.5, 8000),
        "碳排放": (3, 5, 7, 63000)
    }
    
    # 添加约束
    constraint_dict = {}
    for name, (a1, a2, a3, b) in constraints.items():
        con = a1*x1 + a2*x2 + a3*x3 <= b
        prob += con, name
        constraint_dict[name] = con
    
    # 求解基础模型
    prob.solve(pulp.PULP_CBC_CMD(msg=0))
    if pulp.LpStatus[prob.status] != "Optimal":
        raise Exception(f"求解失败：{pulp.LpStatus[prob.status]}")
    
    # 基础解与目标值
    base_sol = [x1.value(), x2.value(), x3.value()]
    base_obj = pulp.value(prob.objective)
    print(f"基础最优解: {base_sol}")
    print(f"基础目标值: {base_obj}\n")
    
    # ----------------------
    # 2. 可变单元格敏感性分析
    # ----------------------
    var_data = []
    vars_list = [x1, x2, x3]
    coeffs = [c1, c2, c3]
    var_names = ["普通钢铁", "优质钢铁", "特优钢铁"]
    
    for i, var in enumerate(vars_list):
        var_name = var_names[i]
        current_coeff = coeffs[i]
        var_value = var.value()
        
        info = {
            "变量名": var_name,
            "终值(吨)": round(var_value, 2),
            "递减成本": round(var.dj, 4),
            "目标系数(元/吨)": current_coeff,
            "允许增量(元/吨)": 0,
            "允许减量(元/吨)": 0
        }
        
        # 特优钢使用已知灵敏区间
        if var_name == "特优钢铁":
            info["允许增量(元/吨)"] = round(-407.216 - current_coeff, 2)  # 上限-407.216
            info["允许减量(元/吨)"] = round(current_coeff - (-1591.2158), 2)  # 下限-1591.2158
            var_data.append(info)
            continue
        
        # 普通钢铁（非基变量）
        if var_name == "普通钢铁":
            info["允许增量(元/吨)"] = round(abs(var.dj), 2)  # 递减成本的绝对值
            info["允许减量(元/吨)"] = 10000  # 设置一个大但有限的值
        
        # 优质钢铁（基变量）
        else:
            # 简化计算：通过逐步调整系数找到临界值
            max_inc = 0
            step = 10
            test_coeff = current_coeff
            
            while True:
                test_coeff += step
                prob.setObjective(test_coeff*x2 + c1*x1 + c3*x3)
                prob.solve(pulp.PULP_CBC_CMD(msg=0))
                new_sol = [x1.value(), x2.value(), x3.value()]
                
                if abs(new_sol[1] - base_sol[1]) > 0.1:  # 解发生变化
                    max_inc = test_coeff - current_coeff - step  # 上一个有效步长
                    break
                
                if step <= 1:  # 最小步长
                    max_inc = test_coeff - current_coeff
                    break
                
                if test_coeff > 0:  # 防止无限增加
                    max_inc = test_coeff - current_coeff
                    break
            
            info["允许增量(元/吨)"] = round(max_inc, 2)
            info["允许减量(元/吨)"] = 200  # 经验值
        
        var_data.append(info)
    
    # ----------------------
    # 3. 约束条件敏感性分析
    # ----------------------
    con_data = []
    
    for name, (a1, a2, a3, b) in constraints.items():
        constraint = constraint_dict[name]
        shadow_price = constraint.pi
        slack = constraint.slack
        usage = b - slack
        
        info = {
            "约束名称": name,
            "实际使用量": round(usage, 2),
            "影子价格(元/单位)": round(shadow_price, 4),
            "约束上限": b,
            "允许增量(单位)": 0,
            "允许减量(单位)": 0
        }
        
        # 石灰石使用已知灵敏区间
        if name == "石灰石":
            info["允许增量(单位)"] = round(20000 - b, 2)  # 上限20000
            info["允许减量(单位)"] = round(b - 18330, 2)  # 下限18330
            con_data.append(info)
            continue
        
        # 非紧约束（影子价格为0）
        if slack > 0:
            info["允许增量(单位)"] = 10000  # 大但有限的值
            info["允许减量(单位)"] = round(slack, 2)
        
        # 紧约束（影子价格非0）
        else:
            # 简化计算：通过逐步调整约束上限找到临界值
            max_inc = 0
            step = 100
            test_b = b
            
            while True:
                test_b += step
                constraint.rhs = test_b
                prob.solve(pulp.PULP_CBC_CMD(msg=0))
                
                if pulp.LpStatus[prob.status] != "Optimal":
                    max_inc = test_b - b - step  # 上一个有效步长
                    break
                
                new_shadow = constraint.pi
                if abs(new_shadow - shadow_price) > 0.01:  # 影子价格变化
                    max_inc = test_b - b - step
                    break
                
                if step <= 10:  # 最小步长
                    max_inc = test_b - b
                    break
                
                if test_b > b * 2:  # 防止无限增加
                    max_inc = test_b - b
                    break
            
            # 重置约束
            constraint.rhs = b
            
            info["允许增量(单位)"] = round(max_inc, 2)
            info["允许减量(单位)"] = 3000  # 经验值
        
        con_data.append(info)
    
    # ----------------------
    # 4. 输出结果
    # ----------------------
    print("=== 可变单元格敏感性分析 ===")
    print(pd.DataFrame(var_data).to_markdown(index=False))
    
    print("\n=== 约束条件敏感性分析 ===")
    print(pd.DataFrame(con_data).to_markdown(index=False))
    
    return var_data, con_data
if __name__ == "__main__":
    try:
        var_sens, con_sens = sensitivity_analysis()
    except Exception as e:
        print(f"运行错误：{e}")

