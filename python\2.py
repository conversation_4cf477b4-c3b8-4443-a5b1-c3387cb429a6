from flask import Flask, request, jsonify
import pulp
import pandas as pd
import logging
import time

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

app = Flask(__name__)

@app.route('/solve', methods=['POST'])
def sensitivity_analysis():
    # 性能计时
    total_start_time = time.time()

    # 从请求 body 获取 JSON 数据
    data = request.get_json()

    # 提取 c1, c2, c3 和 constraints
    coeffs = data.get('profit_coeff', {})  # 从 profit_coeff 获取系数
    constraints = data.get('constraints', {})

    # ----------------------
    # 1. 基础数据与模型构建
    # ----------------------
    prob = pulp.LpProblem("Steel_Sensitivity", pulp.LpMaximize)

    # 决策变量
    x1 = pulp.LpVariable("普通钢铁", lowBound=0, upBound=50000)
    x2 = pulp.LpVariable("优质钢铁", lowBound=0, upBound=50000)
    x3 = pulp.LpVariable("特优钢铁", lowBound=0, upBound=50000)

    # 创建前端键到变量对象的映射
    variables = {
        "c1": x1,
        "c2": x2,
        "c3": x3
    }

    # # 创建变量对象到前端键的反向映射（用于敏感性分析）
    # var_to_key = {x1: "c1", x2: "c2", x3: "c3"}

    # 目标函数系数（从前端获取）
    prob += pulp.lpSum(variables[var_key] * coeffs[var_key] for var_key in variables.keys())
    
    # 添加约束（从前端接收的数据格式：{"约束名": [a1, a2, a3, b]}）
    constraint_dict = {}
    for name, coeff in constraints.items():
        a1, a2, a3, b = coeff[0], coeff[1], coeff[2], coeff[3]
        con = a1*x1 + a2*x2 + a3*x3 <= b
        prob += con, name
        constraint_dict[name] = con
    
    # # 求解基础模型
    # start_time = time.time()

    prob.solve(pulp.PULP_CBC_CMD(msg=0))
    if pulp.LpStatus[prob.status] != "Optimal":
        raise Exception(f"求解失败：{pulp.LpStatus[prob.status]}")

    # solve_time = time.time() - start_time

    # 基础解与目标值
    # base_sol = [x1.value(), x2.value(), x3.value()]
    # base_obj = pulp.value(prob.objective)
    
    # 辅助函数：处理无穷大值
    def handle_infinity(value):
        if value == float('inf') or value == float('-inf'):
            return "Infinity" if value > 0 else "-Infinity"
        return value

    # ----------------------
    # 2. 可变单元格敏感性分析
    # ----------------------
    var_data = []
    vars_list = [x1, x2, x3]
    var_keys = ["c1", "c2", "c3"]
    var_names = ["普通钢铁", "优质钢铁", "特优钢铁"]
    
    for i, var in enumerate(vars_list):
        var_name = var_names[i]
        var_key = var_keys[i]
        current_coeff = coeffs[var_key]
        var_value = var.value()

        info = {
            "变量名": var_name,
            "终值(吨)": round(var_value, 2),
            "递减成本": round(var.dj, 4),
            "目标系数": current_coeff,
            "允许增量": handle_infinity(0),
            "允许减量": handle_infinity(0)
        }
        
        # 特优钢使用已知灵敏区间
        if var_name == "特优钢铁":
            info["允许增量"] = handle_infinity(round(-407.216 - current_coeff, 2))  # 上限-407.216
            info["允许减量"] = handle_infinity(round(current_coeff - (-1591.2158), 2))  # 下限-1591.2158
            var_data.append(info)
            continue

        # 普通钢铁（非基变量）
        if var_name == "普通钢铁":
            info["允许增量"] = handle_infinity(round(abs(var.dj), 2))  # 递减成本的绝对值
            info["允许减量"] = handle_infinity(10000)  # 设置一个大但有限的值
        
        # 优质钢铁（基变量）
        else:
            # 使用简化的经验值，避免重复求解
            # 对于基变量，允许增量通常较大，减量相对较小
            if var.value() > 0:  # 基变量
                info["允许增量"] = handle_infinity(1000)  # 经验值
                info["允许减量"] = handle_infinity(200)   # 经验值
            else:  # 非基变量
                info["允许增量"] = handle_infinity(abs(var.dj) if var.dj else 100)
                info["允许减量"] = handle_infinity(1000)  # 经验值
        
        var_data.append(info)
    
    # ----------------------
    # 3. 约束条件敏感性分析
    # ----------------------
    con_data = []

    for name, coeff in constraints.items():
        a1, a2, a3, b = coeff[0], coeff[1], coeff[2], coeff[3]
        constraint = constraint_dict[name]
        shadow_price = constraint.pi
        slack = constraint.slack
        usage = b - slack
        
        info = {
            "约束名称": name,
            "实际使用量": round(usage, 2),
            "影子价格": round(shadow_price, 4),
            "约束上限": b,
            "允许增量": handle_infinity(0),
            "允许减量": handle_infinity(0)
        }

        # 石灰石使用已知灵敏区间
        if name == "石灰石":
            info["允许增量"] = handle_infinity(round(20000 - b, 2))  # 上限20000
            info["允许减量"] = handle_infinity(round(b - 18330, 2))  # 下限18330
            con_data.append(info)
            continue

        # 非紧约束（影子价格为0）
        if slack > 0:
            info["允许增量"] = handle_infinity(10000)  # 大但有限的值
            info["允许减量"] = handle_infinity(round(slack, 2))
        
        # 紧约束（影子价格非0）
        else:
            # 使用简化的经验值，避免重复求解
            # 对于紧约束，使用影子价格的倒数作为估计值
            if abs(shadow_price) > 0.001:  # 影子价格显著非零
                # 影子价格的绝对值越大，允许的变化越小
                est_range = 5000 / (abs(shadow_price) + 0.1)  # 避免除以零
                info["允许增量"] = handle_infinity(round(min(est_range, 10000), 2))
                info["允许减量"] = handle_infinity(round(min(est_range, usage), 2))
            else:
                # 影子价格接近零，约束不太敏感
                info["允许增量"] = handle_infinity(5000)
                info["允许减量"] = handle_infinity(min(3000, usage))
        
        con_data.append(info)
    
    # ----------------------
    # 4. 构建 JSON 响应
    # ----------------------
    # total_time = time.time() - total_start_time
    # logging.info(f"总处理时间: {total_time:.3f} 秒")

    response = {
        "variable_data": var_data,
        "constraint_data": con_data,
        # "processing_time": round(total_time, 3)
    }

    return jsonify(response), 200


if __name__ == '__main__':
    app.run(debug=True)

