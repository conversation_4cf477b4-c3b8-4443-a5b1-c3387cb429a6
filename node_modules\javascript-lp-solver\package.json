{"name": "javascript-lp-solver", "description": "Easy to use, JSON oriented Linear Programming and Mixed Int. Programming Solver", "version": "0.4.24", "private": false, "authors": ["<PERSON> <<EMAIL>>"], "contributors": ["<PERSON><PERSON> <b<PERSON><PERSON><PERSON>@wizcorp.jp>", "<PERSON><PERSON><PERSON> <<EMAIL>>"], "repository": {"type": "git", "url": "https://github.com/JWally/jsLPSolver"}, "main": "./src/main", "devDependencies": {"benchmark": "*", "grunt": "*", "grunt-browserify": "*", "grunt-contrib-jshint": "*", "grunt-contrib-uglify": "*", "grunt-jsbeautifier": "*", "grunt-mocha-test": "*", "mocha": "*"}, "keywords": ["Linear", "Programming", "Integer", "Programming", "Solver", "Linear Programming", "Linear Optimization", "Simplex", "Mixed Integer Optimization", "Mixed Integer Programming"], "license": "Unlicense"}