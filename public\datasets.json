[{"id": "6c008a6a-64e3-428c-b747-77f4eae0d42e", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 1, "document_count": 1, "word_count": 15032, "created_by": "04f1b4d8-92db-42c6-9e7c-9f72d18d8e9e", "created_at": **********, "updated_by": "04f1b4d8-92db-42c6-9e7c-9f72d18d8e9e", "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "hybrid_search", "reranking_enable": true, "reranking_mode": "reranking_model", "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": {"keyword_setting": {"keyword_weight": 0.3}, "vector_setting": {"vector_weight": 0.7, "embedding_model_name": "", "embedding_provider_name": ""}}, "top_k": 2, "score_threshold_enabled": true, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 2, "score_threshold": 0.5, "score_threshold_enabled": true}, "partial_member_list": []}, {"id": "05f600b7-7bf2-46b5-a700-f76d19280aab", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 2, "document_count": 1, "word_count": 3548, "created_by": "04f1b4d8-92db-42c6-9e7c-9f72d18d8e9e", "created_at": **********, "updated_by": "04f1b4d8-92db-42c6-9e7c-9f72d18d8e9e", "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "hybrid_search", "reranking_enable": true, "reranking_mode": "reranking_model", "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": {"keyword_setting": {"keyword_weight": 0.3}, "vector_setting": {"vector_weight": 0.7, "embedding_model_name": "", "embedding_provider_name": ""}}, "top_k": 3, "score_threshold_enabled": true, "score_threshold": 0.0}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.0, "score_threshold_enabled": true}, "partial_member_list": []}, {"id": "aca8a1e8-1667-4a85-aaf5-c9b5f4b0c083", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 0, "created_by": "7c81626f-9833-466b-a2a4-2ca1a3eba657", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "7cb5747c-56b6-488d-9bdf-d05a228013ea", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 0, "created_by": "7c81626f-9833-466b-a2a4-2ca1a3eba657", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "4e44ac05-ab32-4092-93b9-7db408a3d21b", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 0, "created_by": "7c81626f-9833-466b-a2a4-2ca1a3eba657", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "d3fceb01-ca51-488e-b322-856c10185a3e", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 0, "created_by": "7c81626f-9833-466b-a2a4-2ca1a3eba657", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "dba769ff-dcf1-4bc8-9d55-e1f71d5734b5", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 0, "created_by": "7c81626f-9833-466b-a2a4-2ca1a3eba657", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "d6ef8a39-08ea-4a20-879b-2b7c066c78dd", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 0, "created_by": "7c81626f-9833-466b-a2a4-2ca1a3eba657", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "99a4038d-b3d3-43d9-9ed1-ae6122ac15a5", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 0, "created_by": "7c81626f-9833-466b-a2a4-2ca1a3eba657", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "ff7b0a2f-7a8d-453d-82aa-7e49efa310a1", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 15032, "created_by": "7c81626f-9833-466b-a2a4-2ca1a3eba657", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "773e4c8d-a84d-4d06-a6df-1fe2fe868bdb", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 15032, "created_by": "7c81626f-9833-466b-a2a4-2ca1a3eba657", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "b7dbd301-df7c-469a-af65-726a1c7fa0c9", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 15032, "created_by": "7c81626f-9833-466b-a2a4-2ca1a3eba657", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "6d09a3ff-5c2a-4bc8-8e36-3943debead93", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 3551, "created_by": "7c81626f-9833-466b-a2a4-2ca1a3eba657", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "e0f2ad5b-76b5-41a1-9084-3b77332560d1", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 3551, "created_by": "7c81626f-9833-466b-a2a4-2ca1a3eba657", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "6b80e35b-d750-4d72-b47d-1b759e28aa80", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 3551, "created_by": "7c81626f-9833-466b-a2a4-2ca1a3eba657", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "61fa04a1-90eb-4dbf-ada4-bf8d501b9e1e", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 3551, "created_by": "7c81626f-9833-466b-a2a4-2ca1a3eba657", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "da157f51-2835-4b85-8a62-f73dded96b69", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 3551, "created_by": "7c81626f-9833-466b-a2a4-2ca1a3eba657", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "f580b217-f89d-44a8-86c4-6dcc9d857acc", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 15032, "created_by": "7c81626f-9833-466b-a2a4-2ca1a3eba657", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "564dcb1e-dfb8-4bca-a947-5a15cfb56f26", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 15032, "created_by": "7c81626f-9833-466b-a2a4-2ca1a3eba657", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "527db0d8-9f29-4bf5-8bae-9d10fdc4e69d", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 15032, "created_by": "7c81626f-9833-466b-a2a4-2ca1a3eba657", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "b3f43b8a-44d8-4357-bad0-b49b7830a7c3", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 15032, "created_by": "7c81626f-9833-466b-a2a4-2ca1a3eba657", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "a60c80df-7446-4629-ae88-2fd9116c85dc", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 15032, "created_by": "7c81626f-9833-466b-a2a4-2ca1a3eba657", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "7a23389a-3341-4cb6-a13f-ae26197e2856", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 15032, "created_by": "7c81626f-9833-466b-a2a4-2ca1a3eba657", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "c3da0927-b218-4805-9075-5c040de4d2b2", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 15032, "created_by": "7c81626f-9833-466b-a2a4-2ca1a3eba657", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "58540d1b-c662-49ec-97d3-69e34fa44e66", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 15032, "created_by": "7c81626f-9833-466b-a2a4-2ca1a3eba657", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "b986da74-541c-4b8f-9963-8b620b2d2903", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 3551, "created_by": "7c81626f-9833-466b-a2a4-2ca1a3eba657", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "83c2be01-f226-4bdd-86b2-f5b8505396a8", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 3551, "created_by": "7c81626f-9833-466b-a2a4-2ca1a3eba657", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "53e4c65d-f56d-49b0-8f37-023790eb6c6c", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 3551, "created_by": "7c81626f-9833-466b-a2a4-2ca1a3eba657", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "55253a9a-a695-4559-947b-a867f9918445", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 3551, "created_by": "7c81626f-9833-466b-a2a4-2ca1a3eba657", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "70115699-f153-4658-9640-c624a63ed4c0", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 15032, "created_by": "7c81626f-9833-466b-a2a4-2ca1a3eba657", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "2f52be03-37fc-4063-a69e-6a0cd138bca1", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 15032, "created_by": "7c81626f-9833-466b-a2a4-2ca1a3eba657", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "14ae8b75-8516-4ec1-9cfa-9502f3e9d53e", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 15032, "created_by": "7c81626f-9833-466b-a2a4-2ca1a3eba657", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "12b7d221-b38f-49f4-b2fd-faa740a1aa84", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 3551, "created_by": "7c81626f-9833-466b-a2a4-2ca1a3eba657", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "1bdbe0cb-50dc-4ea0-85ed-5c7c38d7dbb7", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 3551, "created_by": "7c81626f-9833-466b-a2a4-2ca1a3eba657", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "a0a9faed-d834-47c2-85f8-fc4afcfdac2f", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 15032, "created_by": "7c81626f-9833-466b-a2a4-2ca1a3eba657", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "2e9f0ca6-4bc8-49a4-aa45-09936c100e29", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 15032, "created_by": "7c81626f-9833-466b-a2a4-2ca1a3eba657", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "96eef431-9f80-40d3-ab26-8ef57d0056b6", "name": "四川财经1202修改.pdf...", "description": "useful for when you want to answer queries about the 四川财经1202修改.pdf", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 384, "created_by": "0158931d-8c08-432e-bc09-ebdccb161ea2", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "semantic_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "89edfb64-4957-41cd-861d-c89deb657204", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 15032, "created_by": "3f27acc3-1a40-4581-9328-b939b0ed3534", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "35a79228-2f0f-4769-a825-9856783409a5", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 15032, "created_by": "3f27acc3-1a40-4581-9328-b939b0ed3534", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "1a4e8c42-9917-4f61-923f-35dfaef11841", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 0, "created_by": "3f27acc3-1a40-4581-9328-b939b0ed3534", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "8083da57-fcea-4149-a82a-c63ff15eaf80", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 0, "created_by": "3f27acc3-1a40-4581-9328-b939b0ed3534", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "578a2da3-b6fe-4838-825a-402db4f05e3c", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 3551, "created_by": "3f27acc3-1a40-4581-9328-b939b0ed3534", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "94ae646d-fa6d-404f-a84b-9d9b6d60587f", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 0, "created_by": "3f27acc3-1a40-4581-9328-b939b0ed3534", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "a0179a53-3aa8-4620-9865-00d7e985a11c", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 15032, "created_by": "3f27acc3-1a40-4581-9328-b939b0ed3534", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "a79fbaa0-4f62-4c69-8022-83781066506c", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 15032, "created_by": "3f27acc3-1a40-4581-9328-b939b0ed3534", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "d7ae39b6-4287-40d7-a247-866fc6412a74", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 15032, "created_by": "3f27acc3-1a40-4581-9328-b939b0ed3534", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "f7c0f721-898f-412f-8cff-b043c23ff1c8", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 15032, "created_by": "3f27acc3-1a40-4581-9328-b939b0ed3534", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "3d1a7636-c422-41ac-b07d-3a7b4133300b", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 15032, "created_by": "3f27acc3-1a40-4581-9328-b939b0ed3534", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "0c26201d-60a4-4285-a47c-b390b25a78ea", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 0, "created_by": "3f27acc3-1a40-4581-9328-b939b0ed3534", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "7472da60-1d9d-49e2-964c-b82b72b5a430", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 3551, "created_by": "3f27acc3-1a40-4581-9328-b939b0ed3534", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "fea7c356-fb1e-4466-a60e-a2420398c259", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 3551, "created_by": "3f27acc3-1a40-4581-9328-b939b0ed3534", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "86b896e7-2a6e-4587-9c1b-0e16e0b7c4e6", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 3551, "created_by": "3f27acc3-1a40-4581-9328-b939b0ed3534", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "25bef225-3807-4c18-a0a4-c4c49c6d4a46", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 3551, "created_by": "3f27acc3-1a40-4581-9328-b939b0ed3534", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "5d16788f-d51d-4065-b41c-9ea81561acdd", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 3551, "created_by": "3f27acc3-1a40-4581-9328-b939b0ed3534", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "b2f9cb2c-550d-4ccc-9e65-b7bde7f7ac80", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 15032, "created_by": "3f27acc3-1a40-4581-9328-b939b0ed3534", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "99f32472-afa1-466a-a2d2-dae7101977bb", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 15032, "created_by": "3f27acc3-1a40-4581-9328-b939b0ed3534", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "03c3afd0-420a-4d8c-8544-1f37975c0783", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 15032, "created_by": "3f27acc3-1a40-4581-9328-b939b0ed3534", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "88db6e67-92e4-434f-bfc9-438c5b2bd3fe", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 15032, "created_by": "3f27acc3-1a40-4581-9328-b939b0ed3534", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "65eba09b-d3a1-4c04-9067-0a1b9dcd0bbc", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 15032, "created_by": "3f27acc3-1a40-4581-9328-b939b0ed3534", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "5ab21732-0059-463e-8f30-2132d626410b", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 15032, "created_by": "3f27acc3-1a40-4581-9328-b939b0ed3534", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "a18a96eb-6001-4db4-aefe-6542caa57c36", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 15032, "created_by": "3f27acc3-1a40-4581-9328-b939b0ed3534", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "cd845fe1-7624-4952-898b-82f1df784ea9", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 15032, "created_by": "3f27acc3-1a40-4581-9328-b939b0ed3534", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "9141bf85-b55c-449c-8538-cc9d83623a2c", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 15032, "created_by": "3f27acc3-1a40-4581-9328-b939b0ed3534", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "75c4e693-f10e-4ea3-ba08-33baace9525a", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 3551, "created_by": "3f27acc3-1a40-4581-9328-b939b0ed3534", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "4bd72f9b-daff-4628-8da0-5d414610e699", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 3551, "created_by": "3f27acc3-1a40-4581-9328-b939b0ed3534", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "8174fabc-1544-4728-9122-2b08ca2705a4", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 3551, "created_by": "3f27acc3-1a40-4581-9328-b939b0ed3534", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "4496876c-8054-446d-8f41-91878da8da43", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 3551, "created_by": "3f27acc3-1a40-4581-9328-b939b0ed3534", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "ae648d26-2486-4507-8905-68c6a85362a7", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 3551, "created_by": "3f27acc3-1a40-4581-9328-b939b0ed3534", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "b2430831-6ef3-4c03-b9b1-5a06af012422", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 3551, "created_by": "3f27acc3-1a40-4581-9328-b939b0ed3534", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "9b6266b4-77da-4ddd-9ad6-8feb96aed40a", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 3551, "created_by": "3f27acc3-1a40-4581-9328-b939b0ed3534", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "6b84135e-f634-4e0c-be1b-4fe387a95840", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 3551, "created_by": "3f27acc3-1a40-4581-9328-b939b0ed3534", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "b96bec09-61de-4615-82b6-7bcf96767d29", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 3551, "created_by": "3f27acc3-1a40-4581-9328-b939b0ed3534", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "a722ed23-7728-4911-a9ea-23f9fc44b3be", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 15032, "created_by": "3f27acc3-1a40-4581-9328-b939b0ed3534", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "88f25f29-b73c-4470-9e62-0c5e00a2d985", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 3551, "created_by": "3f27acc3-1a40-4581-9328-b939b0ed3534", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "ea3742ec-f9c6-4f0c-8b0c-90e8d51b8f09", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 3551, "created_by": "0158931d-8c08-432e-bc09-ebdccb161ea2", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "semantic_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "01239658-19ea-4d89-8a45-e6ca048f53d0", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 1, "document_count": 1, "word_count": 15032, "created_by": "68361a18-4a68-47de-80b7-8e2b61b71596", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "semantic_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "a63e3f28-7bc4-4d1e-acdd-62bd26d307a1", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 1, "document_count": 1, "word_count": 3551, "created_by": "68361a18-4a68-47de-80b7-8e2b61b71596", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "semantic_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "4c8f1ffa-1e17-4d74-8c86-6bb31801bd49", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 2, "document_count": 1, "word_count": 15032, "created_by": "b2493dbb-f60d-4f59-9978-062bd2ad46fc", "created_at": **********, "updated_by": "b2493dbb-f60d-4f59-9978-062bd2ad46fc", "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "hybrid_search", "reranking_enable": true, "reranking_mode": "weighted_score", "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": {"keyword_setting": {"keyword_weight": 0.3}, "vector_setting": {"vector_weight": 0.7, "embedding_model_name": "", "embedding_provider_name": ""}}, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.0}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.0, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "1137e696-63c7-4674-b8fd-b1e51bf22405", "name": "绿茗轩商品信息.xlsx...", "description": "useful for when you want to answer queries about the 绿茗轩商品信息.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 2299, "created_by": "51c9f0e1-cad3-4fb3-9621-457a9cc950fb", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "semantic_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "hierarchical_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "53101e5b-0f64-42c7-b81a-932d78a4486f", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 1, "document_count": 1, "word_count": 15032, "created_by": "900db6a4-b744-4c1a-a982-5cdb4574dc25", "created_at": **********, "updated_by": "900db6a4-b744-4c1a-a982-5cdb4574dc25", "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "hybrid_search", "reranking_enable": true, "reranking_mode": "weighted_score", "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": {"keyword_setting": {"keyword_weight": 0.3}, "vector_setting": {"vector_weight": 0.7, "embedding_model_name": "bge-large-zh", "embedding_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin"}}, "top_k": 3, "score_threshold_enabled": true, "score_threshold": 0.0}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.0, "score_threshold_enabled": true}, "partial_member_list": []}, {"id": "830b9767-e41c-4504-b9c1-3dd8a3060392", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 1, "document_count": 2, "word_count": 18583, "created_by": "900db6a4-b744-4c1a-a982-5cdb4574dc25", "created_at": **********, "updated_by": "900db6a4-b744-4c1a-a982-5cdb4574dc25", "updated_at": **********, "embedding_model": "doubao-embedding-text-240715", "embedding_model_provider": "langgenius/volcengine_maas/volcengine_maas", "embedding_available": true, "retrieval_model_dict": {"search_method": "hybrid_search", "reranking_enable": true, "reranking_mode": "weighted_score", "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": {"keyword_setting": {"keyword_weight": 0.3}, "vector_setting": {"vector_weight": 0.7, "embedding_model_name": "doubao-embedding-text-240715", "embedding_provider_name": "langgenius/volcengine_maas/volcengine_maas"}}, "top_k": 3, "score_threshold_enabled": true, "score_threshold": 0.01}, "tags": [], "doc_form": "hierarchical_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.01, "score_threshold_enabled": true}, "partial_member_list": []}, {"id": "69ba2ce6-37df-43a0-bf4e-15f47027fca0", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 1, "document_count": 1, "word_count": 15032, "created_by": "7a9f5ece-f913-4fa3-928f-e8aba2efa437", "created_at": **********, "updated_by": "7a9f5ece-f913-4fa3-928f-e8aba2efa437", "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "hybrid_search", "reranking_enable": true, "reranking_mode": "weighted_score", "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": {"keyword_setting": {"keyword_weight": 0.3}, "vector_setting": {"vector_weight": 0.7, "embedding_model_name": "", "embedding_provider_name": ""}}, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.0}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.0, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "1862c7bf-c796-40b2-a830-cb34f5a49119", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 3, "document_count": 1, "word_count": 3551, "created_by": "f0b84fc5-e613-495e-a3b8-04b666c58ec6", "created_at": **********, "updated_by": "f0b84fc5-e613-495e-a3b8-04b666c58ec6", "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "hybrid_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": true, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": true}, "partial_member_list": []}, {"id": "5d5ae0d5-66e6-4e92-adf0-f6ceccb42b38", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 3, "document_count": 1, "word_count": 15032, "created_by": "f0b84fc5-e613-495e-a3b8-04b666c58ec6", "created_at": **********, "updated_by": "f0b84fc5-e613-495e-a3b8-04b666c58ec6", "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "hybrid_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": true, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": true}, "partial_member_list": []}, {"id": "af18692c-1e86-41c0-b022-c227951b4b08", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 1, "document_count": 1, "word_count": 3551, "created_by": "0856dec2-375d-42ae-a0e7-548419b5d7ad", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "hybrid_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "18a3d588-39a2-4628-a2ea-5977f7a3e047", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 1, "document_count": 1, "word_count": 15032, "created_by": "0856dec2-375d-42ae-a0e7-548419b5d7ad", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "hybrid_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "92b45287-f534-4764-b273-d33539a4b41a", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 5, "document_count": 1, "word_count": 15032, "created_by": "c481cc38-1228-4bb1-9987-b6da7d782ebc", "created_at": **********, "updated_by": "c481cc38-1228-4bb1-9987-b6da7d782ebc", "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "hybrid_search", "reranking_enable": true, "reranking_mode": "reranking_model", "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": {"keyword_setting": {"keyword_weight": 0.3}, "vector_setting": {"vector_weight": 0.7, "embedding_model_name": "bge-large-zh", "embedding_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin"}}, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.0}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.0, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "a23f328c-a6b6-4810-894e-fcc1f371029a", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 5, "document_count": 1, "word_count": 3551, "created_by": "c481cc38-1228-4bb1-9987-b6da7d782ebc", "created_at": **********, "updated_by": "c481cc38-1228-4bb1-9987-b6da7d782ebc", "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "hybrid_search", "reranking_enable": true, "reranking_mode": "reranking_model", "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": {"keyword_setting": {"keyword_weight": 0.3}, "vector_setting": {"vector_weight": 0.7, "embedding_model_name": "bge-large-zh", "embedding_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin"}}, "top_k": 4, "score_threshold_enabled": false, "score_threshold": 0.0}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 4, "score_threshold": 0.0, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "a2da3688-4a56-4478-9909-000d45dbbac0", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 3551, "created_by": "d85d8872-3ecf-4131-b755-71e541fb9499", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "hybrid_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "255a3a62-ab50-4267-9156-597b5bf9231e", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 15032, "created_by": "d85d8872-3ecf-4131-b755-71e541fb9499", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "hybrid_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "ae4358a7-eb13-4af0-b347-8686aba3edfa", "name": "文本分析 - 副本.docx...", "description": "useful for when you want to answer queries about the 文本分析 - 副本.docx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 1149, "created_by": "b94bb83d-668c-4cbe-abb8-af56346a2326", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "semantic_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "da4bed74-2e96-4805-8ad1-059dc848eaed", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 1, "document_count": 1, "word_count": 15032, "created_by": "3a85a719-9397-4b45-a561-5d12ee5f9eb0", "created_at": **********, "updated_by": "3a85a719-9397-4b45-a561-5d12ee5f9eb0", "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "hybrid_search", "reranking_enable": true, "reranking_mode": "reranking_model", "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": {"keyword_setting": {"keyword_weight": 0.3}, "vector_setting": {"vector_weight": 0.7, "embedding_model_name": "", "embedding_provider_name": ""}}, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.0}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.0, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "e5ca8920-a299-4336-af08-8c464c8c7c3e", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 1, "document_count": 1, "word_count": 3551, "created_by": "3a85a719-9397-4b45-a561-5d12ee5f9eb0", "created_at": **********, "updated_by": "3a85a719-9397-4b45-a561-5d12ee5f9eb0", "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "hybrid_search", "reranking_enable": true, "reranking_mode": "reranking_model", "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": {"keyword_setting": {"keyword_weight": 0.3}, "vector_setting": {"vector_weight": 0.7, "embedding_model_name": "", "embedding_provider_name": ""}}, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.0}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.0, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "04442cad-fea2-42bf-89ba-6e848a0252a3", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 0, "document_count": 1, "word_count": 3551, "created_by": "68fdf928-09f1-4316-ad1b-5b65897dff97", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "hybrid_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "2c027a2a-76c8-4663-9138-ba1d13c65d30", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 1, "document_count": 1, "word_count": 3551, "created_by": "e2536434-6eb9-4686-a78b-4a9586e70c27", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "hybrid_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "93663d4f-5aac-4536-8b88-fb0e15070f19", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 1, "document_count": 1, "word_count": 3551, "created_by": "ce3b8964-e423-4117-9308-19daed9c723b", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "hybrid_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "1d6908de-62dc-410f-84d2-39e041739bbd", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 1, "document_count": 1, "word_count": 15032, "created_by": "93b3493a-4331-465b-8418-107f299c98a5", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "0f0583cf-c448-477d-b75b-d78f0a396b84", "name": "女装店铺客服常见问题回复.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 1, "document_count": 1, "word_count": 3551, "created_by": "93b3493a-4331-465b-8418-107f299c98a5", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "full_text_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "c244ede7-12a5-4c22-824e-269193e0fb3d", "name": "女装店铺全店宝贝数据.xlsx...", "description": "useful for when you want to answer queries about the 女装店铺全店宝贝数据.xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 1, "document_count": 1, "word_count": 15032, "created_by": "e2536434-6eb9-4686-a78b-4a9586e70c27", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "hybrid_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 3, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 3, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}, {"id": "c6a8555e-fbd0-47d2-abb3-1346fe5a15e2", "name": "女装店铺客服常见问题回复 (1).x...", "description": "useful for when you want to answer queries about the 女装店铺客服常见问题回复 (1).xlsx", "provider": "vendor", "permission": "only_me", "data_source_type": "upload_file", "indexing_technique": "high_quality", "app_count": 1, "document_count": 1, "word_count": 3551, "created_by": "edb2960a-c882-481d-ac3e-49aad1ecc803", "created_at": **********, "updated_by": null, "updated_at": **********, "embedding_model": "bge-large-zh", "embedding_model_provider": "lang<PERSON><PERSON>/wenxin/wenxin", "embedding_available": true, "retrieval_model_dict": {"search_method": "hybrid_search", "reranking_enable": true, "reranking_mode": null, "reranking_model": {"reranking_provider_name": "lang<PERSON><PERSON>/wenxin/wenxin", "reranking_model_name": "bce-reranker-base_v1"}, "weights": null, "top_k": 5, "score_threshold_enabled": false, "score_threshold": 0.5}, "tags": [], "doc_form": "text_model", "external_knowledge_info": {"external_knowledge_id": null, "external_knowledge_api_id": null, "external_knowledge_api_name": null, "external_knowledge_api_endpoint": null}, "external_retrieval_model": {"top_k": 5, "score_threshold": 0.5, "score_threshold_enabled": false}, "partial_member_list": []}]