{"version": 3, "sources": ["../src/solver.js"], "names": ["exports", "module", "require", "r", "e", "n", "t", "o", "i", "f", "c", "u", "a", "Error", "code", "p", "call", "length", "1", "2", "model", "input", "rxo", "is_blank", "is_objective", "is_int", "is_bin", "is_constraint", "is_unrestricted", "parse_lhs", "parse_rhs", "parse_dir", "parse_int", "parse_bin", "get_num", "get_word", "opType", "optimize", "constraints", "variables", ">=", "<=", "=", "tmp", "ary", "hldr", "hldr2", "constraint", "rhs", "split", "test", "match", "map", "d", "replace", "slice", "for<PERSON>ach", "substr", "parseFloat", "_obj", "ints", "binaries", "separatorIndex", "indexOf", "unrestricted", "to_JSON", "output", "lookup", "max", "min", "equal", "rxClean", "RegExp", "x", "xx", "y", "z", "xxx", "xxxx", "from_JSON", "3", "clean_data", "data", "filter", "reduce", "k", "reformat", "solve", "Promise", "res", "rej", "window", "external", "<PERSON><PERSON><PERSON>", "args", "tempName", "writeFile", "fe", "fd", "exec", "execFile", "push", "ret_obj", "meaning", "-2", "4", "5", "6", "7", "9", "25", "255", "./Reformat.js", "child_process", "fs", "lpsolve", "./lpsolve/main.js", "<PERSON><PERSON>", "expressions", "Constraint", "Equality", "Variable", "IntegerVariable", "Term", "Model", "precision", "name", "this", "tableau", "integerVariables", "unrestrictedVariables", "nConstraints", "nVariables", "isMinimization", "tableauInitialized", "relaxationIndex", "useMIRCuts", "checkForCycles", "messages", "prototype", "minimize", "maximize", "_getNewElementIndex", "availableIndexes", "pop", "index", "lastElementIndex", "_addConstraint", "slackVariable", "slack", "variablesPerIndex", "addConstraint", "smallerThan", "getNewElementIndex", "greaterThan", "constraintUpper", "constraintL<PERSON>er", "addVariable", "cost", "id", "isInteger", "isUnrestricted", "priority", "variable", "varIndex", "_removeConstraint", "idx", "splice", "removeConstraint", "relaxation", "removeVariable", "console", "warn", "isEquality", "upperBound", "lowerBound", "updateRightHandSide", "difference", "updateConstraintCoefficient", "setCost", "updateCost", "loadJson", "jsonModel", "constraintsMin", "constraintsMax", "constraintIds", "Object", "keys", "nConstraintIds", "constraintId", "weight", "relaxed", "undefined", "relax", "equality", "variableIds", "tolerance", "timeout", "options", "exitOnCycles", "integerVarIds", "binaryVarIds", "unrestrictedVarIds", "objectiveName", "v", "variableId", "variableConstraints", "isBinary", "addTerm", "constraintNames", "constraintName", "coefficient", "constraintMin", "constraintMax", "getNumberOfIntegerVariables", "setModel", "isFeasible", "feasible", "save", "restore", "activateMIRCuts", "debug", "debugCheckForCycles", "log", "message", "./Tableau/Tableau.js", "./Tableau/branchAndCut.js", "./expressions.js", "solver", "j", "objectives", "new_constraints", "JSON", "parse", "stringify", "counter", "vectors", "vector_key", "obj", "pareto", "Solve", "result", "Math", "random", "cheater", "midpoint", "vertices", "ranges", "Solution", "MilpSolution", "evaluation", "bounded", "branchAndCutIterations", "iter", "create", "constructor", "./Solution.js", "8", "_tableau", "generateSolutionSet", "solutionSet", "varIndexByRow", "matrix", "rhsColumn", "lastRow", "height", "round<PERSON><PERSON><PERSON><PERSON>", "round", "isSlack", "varValue", "Number", "EPSILON", "width", "costRowIndex", "unrestrictedVars", "simplexIters", "varIndexByCol", "rowByVarIndex", "colByVarIndex", "optionalObjectives", "objectivesByPriority", "savedState", "nVars", "unboundedVarIndex", "OptionalObjective", "nColumns", "reducedCosts", "Array", "branchAndCut", "simplex", "updateVariableValues", "getSolution", "copy", "setOptionalObjective", "column", "objectiveForPriority", "sort", "b", "initialize", "tmpRow", "_resetMatrix", "costRow", "coeff", "rowIndex", "term", "constraintIndex", "terms", "nTerms", "row", "isUpperBound", "density", "setEvaluation", "roundedEvaluation", "bestPossibleEval", "./MilpSolution.js", "10", "optionalObjectivesCopy", "matrixCopy", "savedMatrix", "savedRow", "savedBasicIndexes", "savedNonBasicIndexes", "savedRows", "savedCols", "optionalObjectivePerPriority", "optionalObjectiveCopy", "./Tableau.js", "11", "Cut", "type", "value", "Branch", "relaxedEvaluation", "cuts", "sortByEvaluation", "applyCuts", "branchingCuts", "addCutConstraints", "fractionalVolumeImproved", "fractionalVolumeBefore", "computeFractionalVolume", "applyMIRCuts", "branches", "iterations", "toleranceFlag", "terminalTime", "Date", "now", "bestEvaluation", "Infinity", "bestBranch", "bestOptionalObjectivesEvaluations", "oInit", "acceptableThr<PERSON>old", "branch", "isCurrentEvaluationWorse", "isIntegral", "__isIntegral", "oCopy", "getMostFractionalVar", "cutsHigh", "cutsLow", "nCuts", "cut", "ceil", "floor", "cutHigh", "cutLow", "12", "VariableData", "biggestFraction", "selectedVarIndex", "selected<PERSON>ar<PERSON><PERSON><PERSON>", "nIntegerVars", "varRow", "fraction", "abs", "getFractionalVarWithLowestCost", "highestCost", "13", "SlackVariable", "cutConstraints", "nCutConstraints", "heightWithCuts", "h", "lastColumn", "sign", "varRowIndex", "constraintRow", "slackVarIndex", "_addLowerBoundMIRCut", "frac_d", "colIndex", "coef", "<PERSON><PERSON><PERSON><PERSON>", "_addUpperBoundMIRCut", "aj", "fj", "../expressions.js", "14", "_putInBase", "r1", "pivot", "_takeOutOfBase", "pivotRow", "c1", "slackColumn", "nOptionalObjectives", "colVar", "rowVar", "varColumn", "variableRow", "slackIndex", "switchVarIndex", "15", "./backup.js", "./branchingStrategies.js", "./cuttingStrategies.js", "./dynamicModification.js", "./integerProperties.js", "./log.js", "./simplex.js", "16", "count<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "count", "decimalPart", "ignoreIntegerValues", "volume", "17", "force", "varName", "var<PERSON><PERSON><PERSON><PERSON><PERSON>", "valueSpace", "nameSpace", "rowString", "varNameRowString", "spacePerColumn", "firstRow", "firstRowString", "toFixed", "reducedCostsString", "18", "phase1", "phase2", "varIndexesCycle", "leavingRowIndex", "rhsValue", "enteringColumn", "maxQuotient", "leavingRow", "quotient", "cycleData", "reducedCost", "optionalCostsColumns", "enteringValue", "isReducedCostNegative", "optionalCostsColumns2", "minQuotient", "col<PERSON><PERSON>ue", "nonZeroColumns", "pivotRowIndex", "pivotColumnIndex", "leavingBasicIndex", "enteringBasicIndex", "v0", "nNonZeroColumns", "varIndexes", "e1", "e2", "elt1", "elt2", "cycleFound", "tmp1", "tmp2", "19", "CleanObjectiveAttributes", "fakeAttr", "20", "createRelaxationVariable", "termsByVarIndex", "newCoefficient", "setVariableCoefficient", "removeTerm", "setRightHandSide", "newRhs", "_relax", "relaxationVariable", "21", "Solver", "Numeral", "lastSolvedModel", "External", "full", "validate", "validation", "solvers", "solution", "store", "ReformatLP", "MultiObjective", "define", "self", "./External/lpsolve/Reformat.js", "./External/main.js", "./Model", "./Polyopt", "./Tableau/branchAndCut", "./Tableau/index.js", "./Validation"], "mappings": "AAAmC,iBAAZA,UAAuBC,OAAOD,QAAWE,QAAQ,WAC5D,SAASC,EAAEC,EAAEC,EAAEC,GAAG,SAASC,EAAEC,EAAEC,GAAG,IAAIJ,EAAEG,GAAG,CAAC,IAAIJ,EAAEI,GAAG,CAAC,IAAIE,EAAE,mBAAmBR,SAASA,QAAQ,IAAIO,GAAGC,EAAE,OAAOA,EAAEF,GAAE,GAAI,GAAGG,EAAE,OAAOA,EAAEH,GAAE,GAAI,IAAII,EAAE,IAAIC,MAAM,uBAAuBL,EAAE,KAAK,MAAMI,EAAEE,KAAK,mBAAmBF,EAAE,IAAIG,EAAEV,EAAEG,GAAG,CAACR,QAAQ,IAAII,EAAEI,GAAG,GAAGQ,KAAKD,EAAEf,QAAQ,SAASG,GAAoB,OAAOI,EAAlBH,EAAEI,GAAG,GAAGL,IAAeA,IAAIY,EAAEA,EAAEf,QAAQG,EAAEC,EAAEC,EAAEC,GAAG,OAAOD,EAAEG,GAAGR,QAAQ,IAAI,IAAIW,EAAE,mBAAmBT,SAASA,QAAQM,EAAE,EAAEA,EAAEF,EAAEW,OAAOT,IAAID,EAAED,EAAEE,IAAI,OAAOD,EAA7b,CAA4c,CAACW,EAAE,CAAC,SAAShB,EAAQD,EAAOD,KAEte,IAAImB,EAAE,CAAC,SAASjB,EAAQD,EAAOD,GAuSjCC,EAAOD,QAAU,SAAUoB,GAIvB,OAAGA,EAAMH,OA1Rb,SAAiBI,GACb,IAAIC,EAAM,CAENC,SAAY,WACZC,aAAgB,0BAChBC,OAAU,sBACVC,OAAU,sBACVC,cAAiB,iBACjBC,gBAAmB,uBACnBC,UAAc,8DACdC,UAAa,gDACbC,UAAa,kBACbC,UAAa,eACbC,UAAa,eACbC,QAAW,sCACXC,SAAY,cAGhBf,EAAQ,CACJgB,OAAU,GACVC,SAAY,OACZC,YAAe,GACfC,UAAa,IAEjBD,EAAc,CACVE,KAAM,MACNC,KAAM,MACNC,IAAK,SAETC,EAAM,GAAaC,EAAM,KAAMC,EAAO,GAAIC,EAAQ,GAClDC,EAAa,GAAIC,EAAM,EAMH,iBAAV3B,IACNA,EAAQA,EAAM4B,MAAM,OAKxB,IAAI,IAAIzC,EAAI,EAAGA,EAAIa,EAAMJ,OAAQT,IAc7B,GAZAuC,EAAa,KAAOvC,EAGpBmC,EAAMtB,EAAMb,GAGN,EAGNoC,EAAM,KAGHtB,EAAIE,aAAa0B,KAAKP,GAErBvB,EAAMgB,OAASO,EAAIQ,MAAM,eAAe,IAGxCP,EAAMD,EAAIQ,MAAM7B,EAAIO,WAAWuB,IAAI,SAASC,GACxC,OAAOA,EAAEC,QAAQ,MAAM,MACxBC,MAAM,IAMLC,QAAQ,SAASH,GASTR,EAFI,QAJZA,EAAOQ,EAAEF,MAAM7B,EAAIY,UAKM,MAAlBmB,EAAEI,OAAO,EAAE,IACF,EAED,EAGJZ,EAAK,GAGhBA,EAAOa,WAAWb,GAGlBC,EAAQO,EAAEF,MAAM7B,EAAIa,UAAU,GAAGmB,QAAQ,MAAM,IAG/ClC,EAAMmB,UAAUO,GAAS1B,EAAMmB,UAAUO,IAAU,GACnD1B,EAAMmB,UAAUO,GAAOa,KAAOd,SAIhC,GAAGvB,EAAIG,OAAOyB,KAAKP,GAErBC,EAAMD,EAAIQ,MAAM7B,EAAIU,WAAWuB,MAAM,GAGrCnC,EAAMwC,KAAOxC,EAAMwC,MAAQ,GAE3BhB,EAAIY,QAAQ,SAASH,GACjBA,EAAIA,EAAEC,QAAQ,IAAI,IAClBlC,EAAMwC,KAAKP,GAAK,SAGjB,GAAG/B,EAAII,OAAOwB,KAAKP,GAEtBC,EAAMD,EAAIQ,MAAM7B,EAAIW,WAAWsB,MAAM,GAGrCnC,EAAMyC,SAAWzC,EAAMyC,UAAY,GAEnCjB,EAAIY,QAAQ,SAASH,GACjBA,EAAIA,EAAEC,QAAQ,IAAI,IAClBlC,EAAMyC,SAASR,GAAK,SAGrB,GAAG/B,EAAIK,cAAcuB,KAAKP,GAAK,CAClC,IAAImB,EAAiBnB,EAAIoB,QAAQ,MAIjCnB,IAHgD,IAApBkB,EAAyBnB,EAAMA,EAAIY,MAAMO,EAAiB,IAG3DX,MAAM7B,EAAIO,WAAWuB,IAAI,SAASC,GACzD,OAAOA,EAAEC,QAAQ,MAAM,OAKvBE,QAAQ,SAASH,GAMTR,EAFI,QAFZA,EAAOQ,EAAEF,MAAM7B,EAAIY,UAGM,MAAlBmB,EAAEI,OAAO,EAAE,IACF,EAED,EAGJZ,EAAK,GAGhBA,EAAOa,WAAWb,GAIlBC,EAAQO,EAAEF,MAAM7B,EAAIa,UAAU,GAG9Bf,EAAMmB,UAAUO,GAAS1B,EAAMmB,UAAUO,IAAU,GACnD1B,EAAMmB,UAAUO,GAAOC,GAAcF,IAMzCG,EAAMU,WAAWf,EAAIQ,MAAM7B,EAAIQ,WAAW,IAI1Ca,EAAML,EAAYK,EAAIQ,MAAM7B,EAAIS,WAAW,IAC3CX,EAAMkB,YAAYS,GAAc3B,EAAMkB,YAAYS,IAAe,GACjE3B,EAAMkB,YAAYS,GAAYJ,GAAOK,OAE/B1B,EAAIM,gBAAgBsB,KAAKP,KAE/BC,EAAMD,EAAIQ,MAAM7B,EAAIU,WAAWuB,MAAM,GAGrCnC,EAAM4C,aAAe5C,EAAM4C,cAAgB,GAE3CpB,EAAIY,QAAQ,SAASH,GACjBA,EAAIA,EAAEC,QAAQ,IAAI,IAClBlC,EAAM4C,aAAaX,GAAK,KAIpC,OAAOjC,EAsGI6C,CAAQ7C,GA1FvB,SAAmBA,GAEf,IAAKA,EACD,MAAM,IAAIP,MAAM,yCAGpB,IAAIqD,EAAS,GAGTC,EAAS,CACLC,IAAO,KACPC,IAAO,KACPC,MAAS,KAEbC,EAAU,IAAIC,OAAO,6BAA2C,MAIpE,GAAGpD,EAAMgB,OAKL,IAAI,IAAIqC,KAHRP,GAAU9C,EAAMgB,OAAS,IAGZhB,EAAMmB,UAGfnB,EAAMmB,UAAUkC,GAAGA,GAAKrD,EAAMmB,UAAUkC,GAAGA,GAAKrD,EAAMmB,UAAUkC,GAAGA,GAAK,EAGrErD,EAAMmB,UAAUkC,GAAGrD,EAAMiB,YACxB6B,GAAU,IAAM9C,EAAMmB,UAAUkC,GAAGrD,EAAMiB,UAAY,IAAMoC,EAAEnB,QAAQiB,EAAQ,WAIrFL,GAAU,OASd,IAAI,IAAIQ,KAHRR,GAAU,QAGI9C,EAAMkB,YAChB,IAAI,IAAIqC,KAAKvD,EAAMkB,YAAYoC,GAC3B,QAAwB,IAAdP,EAAOQ,GAAmB,CAEhC,IAAI,IAAIC,KAAKxD,EAAMmB,eAGsB,IAA3BnB,EAAMmB,UAAUqC,GAAGF,KACzBR,GAAU,IAAM9C,EAAMmB,UAAUqC,GAAGF,GAAM,IAAME,EAAEtB,QAAQiB,EAAQ,MAKzEL,GAAU,IAAMC,EAAOQ,GAAK,IAAMvD,EAAMkB,YAAYoC,GAAIC,GACxDT,GAAU,MAOtB,GAAG9C,EAAMwC,KAEL,IAAI,IAAIiB,KADRX,GAAU,OACK9C,EAAMwC,KACjBM,GAAU,OAASW,EAAIvB,QAAQiB,EAAQ,KAAO,MAKtD,GAAGnD,EAAM4C,aAEL,IAAI,IAAIc,KADRZ,GAAU,OACM9C,EAAM4C,aAClBE,GAAU,gBAAkBY,EAAKxB,QAAQiB,EAAQ,KAAO,MAKhE,OAAOL,EAYIa,CAAU3D,KAIvB,IAAI4D,EAAE,CAAC,SAAS9E,EAAQD,EAAOD,GAmBjC,SAASiF,EAAWC,GAuChB,OA7BAA,GADAA,GAHAA,EAAOA,EAAK5B,QAAQ,SAAS,SAGjBL,MAAM,SACNkC,OAAO,SAASV,GAOxB,OAAkB,IADb,IAAID,OAAO,MAAM,MAChBtB,KAAKuB,KAOO,IADb,IAAID,OAAO,OAAO,MACjBtB,KAAKuB,KAOdrB,IAAI,SAASqB,GACV,OAAOA,EAAExB,MAAM,qBAElBmC,OAAO,SAAS7E,EAAE8E,EAAE7E,GAEjB,OADAD,EAAE8E,EAAE,IAAMA,EAAE,GACL9E,GACT,IAvCNP,EAAQsF,SAAWpF,EAAQ,iBAgD3BF,EAAQuF,MAAQ,SAASnE,GAErB,OAAO,IAAIoE,QAAQ,SAASC,EAAKC,GAIR,oBAAXC,QACND,EAAI,qCAKR,IAAIR,EAAOhF,EAAQ,gBAARA,CAAyBkB,GAGhCA,EAAMwE,UACNF,EAAI,oGAOJtE,EAAMwE,SAASC,SACfH,EAAI,kEAMJtE,EAAMwE,SAASE,MACfJ,EAAI,kEAOJtE,EAAMwE,SAASG,UACfL,EAAI,iGAkBCxF,EAAQ,MAEd8F,UAAU5E,EAAMwE,SAASG,SAAUb,EAAM,SAASe,EAAIC,GACrD,GAAGD,EACCP,EAAIO,OACD,CAMH,IAAIE,EAAOjG,EAAQ,iBAAiBkG,SAKpChF,EAAMwE,SAASE,KAAKO,KAAKjF,EAAMwE,SAASG,UAExCI,EAAK/E,EAAMwE,SAASC,QAASzE,EAAMwE,SAASE,KAAM,SAAS1F,EAAE8E,GACzD,GAAG9E,EAEC,GAAc,IAAXA,EAAEU,KACD2E,EAAIR,EAAWC,QACZ,CAEH,IAcIoB,EAAU,CACVxF,KAAQV,EAAEU,KACVyF,QAhBQ,CACRC,KAAM,gBACNtF,EAAK,aACLC,EAAK,aACL6D,EAAK,YACLyB,EAAK,aACLC,EAAK,aACLC,EAAK,aACLC,EAAK,UACLC,EAAK,YACLC,GAAM,iBACNC,IAAO,cAKU3G,EAAEU,MACnBoE,KAAQA,GAGZQ,EAAIY,QAKRb,EAAIR,EAAWC,aAuBrC,CAAC8B,gBAAgB,EAAEC,cAAgB,EAAEC,GAAK,IAAIT,EAAE,CAAC,SAASvG,EAAQD,EAAOD,GAU3EC,EAAOD,QAAU,CACbmH,QAAWjH,EAAQ,uBAErB,CAACkH,oBAAoB,IAAIV,EAAE,CAAC,SAASxG,EAAQD,EAAOD,GAQtD,IAAIqH,EAAUnH,EAAQ,wBAElBoH,GADepH,EAAQ,6BACTA,EAAQ,qBACtBqH,EAAaD,EAAYC,WACzBC,EAAWF,EAAYE,SACvBC,EAAWH,EAAYG,SACvBC,EAAkBJ,EAAYI,gBACvBJ,EAAYK,KAMvB,SAASC,EAAMC,EAAWC,GACtBC,KAAKC,QAAU,IAAIX,EAAQQ,GAE3BE,KAAKD,KAAOA,EAEZC,KAAKxF,UAAY,GAEjBwF,KAAKE,iBAAmB,GAExBF,KAAKG,sBAAwB,GAE7BH,KAAKzF,YAAc,GAEnByF,KAAKI,aAAe,EAEpBJ,KAAKK,WAAa,EAElBL,KAAKM,gBAAiB,EAEtBN,KAAKO,oBAAqB,EAE1BP,KAAKQ,gBAAkB,EAEvBR,KAAKS,YAAa,EAElBT,KAAKU,gBAAiB,EAOtBV,KAAKW,SAAW,IAEpBzI,EAAOD,QAAU4H,GAEXe,UAAUC,SAAW,WAEvB,OADAb,KAAKM,gBAAiB,EACfN,MAGXH,EAAMe,UAAUE,SAAW,WAEvB,OADAd,KAAKM,gBAAiB,EACfN,MAUXH,EAAMe,UAAUG,oBAAsB,WAClC,GAAmC,EAA/Bf,KAAKgB,iBAAiB9H,OACtB,OAAO8G,KAAKgB,iBAAiBC,MAGjC,IAAIC,EAAQlB,KAAKmB,iBAEjB,OADAnB,KAAKmB,kBAAoB,EAClBD,GAGXrB,EAAMe,UAAUQ,eAAiB,SAAUpG,GACvC,IAAIqG,EAAgBrG,EAAWsG,MAC/BtB,KAAKC,QAAQsB,kBAAkBF,EAAcH,OAASG,EACtDrB,KAAKzF,YAAY+D,KAAKtD,GACtBgF,KAAKI,cAAgB,GACW,IAA5BJ,KAAKO,oBACLP,KAAKC,QAAQuB,cAAcxG,IAInC6E,EAAMe,UAAUa,YAAc,SAAUxG,GACpC,IAAID,EAAa,IAAIwE,EAAWvE,GAAK,EAAM+E,KAAKC,QAAQyB,qBAAsB1B,MAE9E,OADAA,KAAKoB,eAAepG,GACbA,GAGX6E,EAAMe,UAAUe,YAAc,SAAU1G,GACpC,IAAID,EAAa,IAAIwE,EAAWvE,GAAK,EAAO+E,KAAKC,QAAQyB,qBAAsB1B,MAE/E,OADAA,KAAKoB,eAAepG,GACbA,GAGX6E,EAAMe,UAAUrE,MAAQ,SAAUtB,GAC9B,IAAI2G,EAAkB,IAAIpC,EAAWvE,GAAK,EAAM+E,KAAKC,QAAQyB,qBAAsB1B,MACnFA,KAAKoB,eAAeQ,GAEpB,IAAIC,EAAkB,IAAIrC,EAAWvE,GAAK,EAAO+E,KAAKC,QAAQyB,qBAAsB1B,MAGpF,OAFAA,KAAKoB,eAAeS,GAEb,IAAIpC,EAASmC,EAAiBC,IAGzChC,EAAMe,UAAUkB,YAAc,SAAUC,EAAMC,EAAIC,EAAWC,EAAgBC,GACzE,GAAwB,iBAAbA,EACP,OAAQA,GACR,IAAK,WACDA,EAAW,EACX,MACJ,IAAK,SACDA,EAAW,EACX,MACJ,IAAK,SACDA,EAAW,EACX,MACJ,IAAK,OACDA,EAAW,EACX,MACJ,QACIA,EAAW,EAKnB,IAaIC,EAbAC,EAAWrC,KAAKC,QAAQyB,qBAkC5B,OAjCIM,MAAAA,IACAA,EAAK,IAAMK,GAGXN,MAAAA,IACAA,EAAO,GAGPI,MAAAA,IACAA,EAAW,GAIXF,GACAG,EAAW,IAAIzC,EAAgBqC,EAAID,EAAMM,EAAUF,GACnDnC,KAAKE,iBAAiB5B,KAAK8D,IAE3BA,EAAW,IAAI1C,EAASsC,EAAID,EAAMM,EAAUF,GAGhDnC,KAAKxF,UAAU8D,KAAK8D,GACpBpC,KAAKC,QAAQsB,kBAAkBc,GAAYD,EAEvCF,IACAlC,KAAKG,sBAAsBkC,IAAY,GAG3CrC,KAAKK,YAAc,GAEa,IAA5BL,KAAKO,oBACLP,KAAKC,QAAQ6B,YAAYM,GAGtBA,GAGXvC,EAAMe,UAAU0B,kBAAoB,SAAUtH,GAC1C,IAAIuH,EAAMvC,KAAKzF,YAAYyB,QAAQhB,IACtB,IAATuH,GAKJvC,KAAKzF,YAAYiI,OAAOD,EAAK,GAC7BvC,KAAKI,cAAgB,GAEW,IAA5BJ,KAAKO,oBACLP,KAAKC,QAAQwC,iBAAiBzH,GAG9BA,EAAW0H,YACX1C,KAAK2C,eAAe3H,EAAW0H,aAZ/BE,QAAQC,KAAK,6DAmBrBhD,EAAMe,UAAU6B,iBAAmB,SAAUzH,GAQzC,OAPIA,EAAW8H,YACX9C,KAAKsC,kBAAkBtH,EAAW+H,YAClC/C,KAAKsC,kBAAkBtH,EAAWgI,aAElChD,KAAKsC,kBAAkBtH,GAGpBgF,MAGXH,EAAMe,UAAU+B,eAAiB,SAAUP,GACvC,IAAIG,EAAMvC,KAAKxF,UAAUwB,QAAQoG,GACjC,IAAa,IAATG,EAUJ,OANAvC,KAAKxF,UAAUgI,OAAOD,EAAK,IAEK,IAA5BvC,KAAKO,oBACLP,KAAKC,QAAQ0C,eAAeP,GAGzBpC,KATH4C,QAAQC,KAAK,yDAYrBhD,EAAMe,UAAUqC,oBAAsB,SAAUjI,EAAYkI,GAIxD,OAHgC,IAA5BlD,KAAKO,oBACLP,KAAKC,QAAQgD,oBAAoBjI,EAAYkI,GAE1ClD,MAGXH,EAAMe,UAAUuC,4BAA8B,SAAUnI,EAAYoH,EAAUc,GAI1E,OAHgC,IAA5BlD,KAAKO,oBACLP,KAAKC,QAAQkD,4BAA4BnI,EAAYoH,EAAUc,GAE5DlD,MAIXH,EAAMe,UAAUwC,QAAU,SAAUrB,EAAMK,GACtC,IAAIc,EAAanB,EAAOK,EAASL,KAOjC,OAN4B,IAAxB/B,KAAKM,iBACL4C,GAAcA,GAGlBd,EAASL,KAAOA,EAChB/B,KAAKC,QAAQoD,WAAWjB,EAAUc,GAC3BlD,MAKXH,EAAMe,UAAU0C,SAAW,SAAUC,GACjCvD,KAAKM,eAAuC,QAArBiD,EAAUlJ,OAYjC,IAVA,IAAIG,EAAY+I,EAAU/I,UACtBD,EAAcgJ,EAAUhJ,YAExBiJ,EAAiB,GACjBC,EAAiB,GAGjBC,EAAgBC,OAAOC,KAAKrJ,GAC5BsJ,EAAiBH,EAAcxK,OAE1BP,EAAI,EAAGA,EAAIkL,EAAgBlL,GAAK,EAAG,CACxC,IAQIqK,EAAYD,EARZe,EAAeJ,EAAc/K,GAC7BqC,EAAaT,EAAYuJ,GACzBvH,EAAQvB,EAAWuB,MAEnBwH,EAAS/I,EAAW+I,OACpB5B,EAAWnH,EAAWmH,SACtB6B,OAAqBC,IAAXF,QAAqCE,IAAb9B,EAGtC,QAAc8B,IAAV1H,EAAqB,CACrB,IAAID,EAAMtB,EAAWsB,SACT2H,IAAR3H,IACA0G,EAAahD,KAAK2B,YAAYrF,GAC9BkH,EAAeM,GAAgBd,EAC3BgB,GAAWhB,EAAWkB,MAAMH,EAAQ5B,IAG5C,IAAI9F,EAAMrB,EAAWqB,SACT4H,IAAR5H,IACA0G,EAAa/C,KAAKyB,YAAYpF,GAC9BoH,EAAeK,GAAgBf,EAC3BiB,GAAWjB,EAAWmB,MAAMH,EAAQ5B,QAEzC,CACHa,EAAahD,KAAK2B,YAAYpF,GAC9BiH,EAAeM,GAAgBd,EAE/BD,EAAa/C,KAAKyB,YAAYlF,GAC9BkH,EAAeK,GAAgBf,EAE/B,IAAIoB,EAAW,IAAI1E,EAASuD,EAAYD,GACpCiB,GAAWG,EAASD,MAAMH,EAAQ5B,IAI9C,IAAIiC,EAAcT,OAAOC,KAAKpJ,GAC1B6F,EAAa+D,EAAYlL,OAU7B8G,KAAKqE,UAAYd,EAAUc,WAAa,EAErCd,EAAUe,UACTtE,KAAKsE,QAAUf,EAAUe,SAa1Bf,EAAUgB,UAKNhB,EAAUgB,QAAQD,UACjBtE,KAAKsE,QAAUf,EAAUgB,QAAQD,SAMf,IAAnBtE,KAAKqE,YACJrE,KAAKqE,UAAYd,EAAUgB,QAAQF,WAAa,GAMjDd,EAAUgB,QAAQ9D,aACjBT,KAAKS,WAAa8C,EAAUgB,QAAQ9D,iBASK,IAAnC8C,EAAUgB,QAAQC,aACxBxE,KAAKU,gBAAiB,EAEtBV,KAAKU,eAAiB6C,EAAUgB,QAAQC,cAmBhD,IANA,IAAIC,EAAgBlB,EAAU1H,MAAQ,GAClC6I,EAAenB,EAAUzH,UAAY,GACrC6I,EAAqBpB,EAAUtH,cAAgB,GAG/C2I,EAAgBrB,EAAUjJ,SACrBuK,EAAI,EAAGA,EAAIxE,EAAYwE,GAAK,EAAG,CAEpC,IAAIC,EAAaV,EAAYS,GACzBE,EAAsBvK,EAAUsK,GAChC/C,EAAOgD,EAAoBH,IAAkB,EAC7CI,IAAaN,EAAaI,GAC1B7C,IAAcwC,EAAcK,IAAeE,EAC3C9C,IAAmByC,EAAmBG,GACtC1C,EAAWpC,KAAK8B,YAAYC,EAAM+C,EAAY7C,EAAWC,GAEzD8C,GAEAhF,KAAKyB,YAAY,GAAGwD,QAAQ,EAAG7C,GAGnC,IAAI8C,EAAkBvB,OAAOC,KAAKmB,GAClC,IAAKpM,EAAI,EAAGA,EAAIuM,EAAgBhM,OAAQP,GAAK,EAAG,CAC5C,IAAIwM,EAAiBD,EAAgBvM,GACrC,GAAIwM,IAAmBP,EAAvB,CAIA,IAAIQ,EAAcL,EAAoBI,GAElCE,EAAgB7B,EAAe2B,QACblB,IAAlBoB,GACAA,EAAcJ,QAAQG,EAAahD,GAGvC,IAAIkD,EAAgB7B,EAAe0B,QACblB,IAAlBqB,GACAA,EAAcL,QAAQG,EAAahD,KAK/C,OAAOpC,MAKXH,EAAMe,UAAU2E,4BAA8B,WAC1C,OAAOvF,KAAKE,iBAAiBhH,QAGjC2G,EAAMe,UAAUpD,MAAQ,WAOpB,OALgC,IAA5BwC,KAAKO,qBACLP,KAAKC,QAAQuF,SAASxF,MACtBA,KAAKO,oBAAqB,GAGvBP,KAAKC,QAAQzC,SAGxBqC,EAAMe,UAAU6E,WAAa,WACzB,OAAOzF,KAAKC,QAAQyF,UAGxB7F,EAAMe,UAAU+E,KAAO,WACnB,OAAO3F,KAAKC,QAAQ0F,QAGxB9F,EAAMe,UAAUgF,QAAU,WACtB,OAAO5F,KAAKC,QAAQ2F,WAGxB/F,EAAMe,UAAUiF,gBAAkB,SAAUpF,GACxCT,KAAKS,WAAaA,GAGtBZ,EAAMe,UAAUkF,MAAQ,SAAUC,GAC9B/F,KAAKU,eAAiBqF,GAG1BlG,EAAMe,UAAUoF,IAAM,SAAUC,GAC5B,OAAOjG,KAAKC,QAAQ+F,IAAIC,KAG1B,CAACC,uBAAuB,EAAEC,4BAA4B,GAAGC,mBAAmB,KAAKxH,EAAE,CAAC,SAASzG,EAAQD,EAAOD,GAsC9GC,EAAOD,QAAU,SAASoO,EAAQhN,GAc9B,IAGIuB,EAMAnC,EAAE6N,EAAE5J,EAAEE,EATN2J,EAAalN,EAAMiB,SACnBkM,EAAkBC,KAAKC,MAAMD,KAAKE,UAAUtN,EAAMiB,WAClDsJ,EAAOD,OAAOC,KAAKvK,EAAMiB,UAEzBsM,EAAU,EACVC,EAAU,GACVC,EAAa,GACbC,EAAM,GACNC,EAAS,GAOb,WAHO3N,EAAMiB,SAGT7B,EAAI,EAAGA,EAAImL,EAAK1K,OAAQT,IAExB+N,EAAgB5C,EAAKnL,IAAM,EAI/B,IAAIA,EAAI,EAAGA,EAAImL,EAAK1K,OAAQT,IAAI,CAiB5B,IAAImE,KAdJvD,EAAMiB,SAAWsJ,EAAKnL,GACtBY,EAAMgB,OAASkM,EAAW3C,EAAKnL,IAG/BmC,EAAMyL,EAAOY,MAAM5N,OAAO4K,OAAWA,GAAW,GAUvCL,EAEL,IAAIvK,EAAMmB,UAAUoJ,EAAKhH,IAIrB,IAAIF,KAFJ9B,EAAIgJ,EAAKhH,IAAMhC,EAAIgJ,EAAKhH,IAAMhC,EAAIgJ,EAAKhH,IAAM,EAEpCvD,EAAMmB,UAERnB,EAAMmB,UAAUkC,GAAGkH,EAAKhH,KAAOhC,EAAI8B,KAElC9B,EAAIgJ,EAAKhH,KAAOhC,EAAI8B,GAAKrD,EAAMmB,UAAUkC,GAAGkH,EAAKhH,KAYjE,IALAkK,EAAa,OAKTR,EAAI,EAAGA,EAAI1C,EAAK1K,OAAQoN,IACrB1L,EAAIgJ,EAAK0C,IACRQ,GAAc,KAAuB,IAAflM,EAAIgJ,EAAK0C,IAAc,GAAK,IAElDQ,GAAc,KAKtB,IAAID,EAAQC,GAAY,CAOpB,IALAD,EAAQC,GAAc,EACtBF,IAIIN,EAAI,EAAGA,EAAI1C,EAAK1K,OAAQoN,IACrB1L,EAAIgJ,EAAK0C,MACRE,EAAgB5C,EAAK0C,KAAO1L,EAAIgJ,EAAK0C,YAQtC1L,EAAI8K,gBACJ9K,EAAIsM,OACXF,EAAO1I,KAAK1D,IASpB,IAAInC,EAAI,EAAGA,EAAImL,EAAK1K,OAAQT,IACxBY,EAAMkB,YAAYqJ,EAAKnL,IAAM,CAAC8D,MAASiK,EAAgB5C,EAAKnL,IAAMmO,GAStE,IAAInO,KALJY,EAAMiB,SAAW,WAAa6M,KAAKC,SACnC/N,EAAMgB,OAAS,MAINhB,EAAMmB,UACXnB,EAAMmB,UAAU/B,GAAG4O,QAAU,EAIjC,IAAI5O,KAAKuO,EACL,IAAItK,KAAKsK,EAAOvO,GACZsO,EAAIrK,GAAKqK,EAAIrK,IAAM,CAACJ,IAAK,KAAMD,KAAM,MAO7C,IAAI5D,KAAKsO,EACL,IAAIrK,KAAKsK,EACFA,EAAOtK,GAAGjE,IACNuO,EAAOtK,GAAGjE,GAAKsO,EAAItO,GAAG4D,MACrB0K,EAAItO,GAAG4D,IAAM2K,EAAOtK,GAAGjE,IAExBuO,EAAOtK,GAAGjE,GAAKsO,EAAItO,GAAG6D,MACrByK,EAAItO,GAAG6D,IAAM0K,EAAOtK,GAAGjE,MAG3BuO,EAAOtK,GAAGjE,GAAK,EACfsO,EAAItO,GAAG6D,IAAM,GAOzB,MAAO,CACHgL,SAHJ1M,EAAOyL,EAAOY,MAAM5N,OAAO4K,OAAWA,GAAW,GAI7CsD,SAAUP,EACVQ,OAAQT,KAKd,IAAIlI,EAAE,CAAC,SAAS1G,EAAQD,EAAOD,GAGjC,IAAIwP,EAAWtP,EAAQ,iBAEvB,SAASuP,EAAazH,EAAS0H,EAAYjC,EAAUkC,EAASC,GAC1DJ,EAASxO,KAAK+G,KAAMC,EAAS0H,EAAYjC,EAAUkC,GACnD5H,KAAK8H,KAAOD,GAEhB3P,EAAOD,QAAUyP,GACJ9G,UAAY+C,OAAOoE,OAAON,EAAS7G,WAChD8G,EAAaM,YAAcN,GAEzB,CAACO,gBAAgB,IAAIC,EAAE,CAAC,SAAS/P,EAAQD,EAAOD,GAGlD,SAASwP,EAASxH,EAAS0H,EAAYjC,EAAUkC,GAC7C5H,KAAK0F,SAAWA,EAChB1F,KAAK2H,WAAaA,EAClB3H,KAAK4H,QAAUA,EACf5H,KAAKmI,SAAWlI,GAEpB/H,EAAOD,QAAUwP,GAER7G,UAAUwH,oBAAsB,WAWrC,IAVA,IAAIC,EAAc,GAEdpI,EAAUD,KAAKmI,SACfG,EAAgBrI,EAAQqI,cACxB/G,EAAoBtB,EAAQsB,kBAC5BgH,EAAStI,EAAQsI,OACjBC,EAAYvI,EAAQuI,UACpBC,EAAUxI,EAAQyI,OAAS,EAC3BC,EAAgBxB,KAAKyB,MAAM,EAAI3I,EAAQH,WAElC1H,EAAI,EAAGA,GAAKqQ,EAASrQ,GAAK,EAAG,CAClC,IACIgK,EAAWb,EADA+G,EAAclQ,IAE7B,QAAiB6L,IAAb7B,IAA+C,IAArBA,EAASyG,QAAvC,CAIA,IAAIC,EAAWP,EAAOnQ,GAAGoQ,GACzBH,EAAYjG,EAASJ,IACjBmF,KAAKyB,OAAOG,OAAOC,QAAUF,GAAYH,GAAiBA,GAGlE,OAAON,IAGT,IAAIvJ,EAAE,CAAC,SAAS3G,EAAQD,EAAOD,GAOjC,IAAIwP,EAAWtP,EAAQ,iBACnBuP,EAAevP,EAAQ,qBAa3B,SAASmH,EAAQQ,GACbE,KAAK3G,MAAQ,KAEb2G,KAAKuI,OAAS,KACdvI,KAAKiJ,MAAQ,EACbjJ,KAAK0I,OAAS,EAEd1I,KAAKkJ,aAAe,EACpBlJ,KAAKwI,UAAY,EAEjBxI,KAAKuB,kBAAoB,GACzBvB,KAAKmJ,iBAAmB,KAGxBnJ,KAAK0F,UAAW,EAChB1F,KAAK2H,WAAa,EAClB3H,KAAKoJ,aAAe,EAEpBpJ,KAAKsI,cAAgB,KACrBtI,KAAKqJ,cAAgB,KAErBrJ,KAAKsJ,cAAgB,KACrBtJ,KAAKuJ,cAAgB,KAErBvJ,KAAKF,UAAYA,GAAa,KAE9BE,KAAKwJ,mBAAqB,GAC1BxJ,KAAKyJ,qBAAuB,GAE5BzJ,KAAK0J,WAAa,KAElB1J,KAAKgB,iBAAmB,GACxBhB,KAAKmB,iBAAmB,EAExBnB,KAAKxF,UAAY,KACjBwF,KAAK2J,MAAQ,EAEb3J,KAAK4H,SAAU,EACf5H,KAAK4J,kBAAoB,KAEzB5J,KAAK6H,uBAAyB,EAclC,SAASgC,EAAkB1H,EAAU2H,GACjC9J,KAAKmC,SAAWA,EAChBnC,KAAK+J,aAAe,IAAIC,MAAMF,GAC9B,IAAK,IAAInR,EAAI,EAAGA,EAAImR,EAAUnR,GAAK,EAC/BqH,KAAK+J,aAAapR,GAAK,GAhB/BT,EAAOD,QAAUqH,GAETsB,UAAUpD,MAAQ,WAOtB,OAN+C,EAA3CwC,KAAK3G,MAAMkM,8BACXvF,KAAKiK,eAELjK,KAAKkK,UAETlK,KAAKmK,uBACEnK,KAAKoK,eAWhBP,EAAkBjJ,UAAUyJ,KAAO,WAC/B,IAAIA,EAAO,IAAIR,EAAkB7J,KAAKmC,SAAUnC,KAAK+J,aAAa7Q,QAElE,OADAmR,EAAKN,aAAe/J,KAAK+J,aAAavO,QAC/B6O,GAGX/K,EAAQsB,UAAU0J,qBAAuB,SAAUnI,EAAUoI,EAAQxI,GACjE,IAAIyI,EAAuBxK,KAAKyJ,qBAAqBtH,QACxB8B,IAAzBuG,IAEAA,EAAuB,IAAIX,EAAkB1H,EAD9BgF,KAAK9K,IAAI2D,KAAKiJ,MAAOsB,EAAS,IAE7CvK,KAAKyJ,qBAAqBtH,GAAYqI,EACtCxK,KAAKwJ,mBAAmBlL,KAAKkM,GAC7BxK,KAAKwJ,mBAAmBiB,KAAK,SAAU5R,EAAG6R,GACtC,OAAO7R,EAAEsJ,SAAWuI,EAAEvI,YAI9BqI,EAAqBT,aAAaQ,GAAUxI,GAKhDzC,EAAQsB,UAAU+J,WAAa,SAAU1B,EAAOP,EAAQlO,EAAW2O,GAC/DnJ,KAAKxF,UAAYA,EACjBwF,KAAKmJ,iBAAmBA,EAExBnJ,KAAKiJ,MAAQA,EACbjJ,KAAK0I,OAASA,EAMd,IADA,IAAIkC,EAAS,IAAIZ,MAAMf,GACdxQ,EAAI,EAAGA,EAAIwQ,EAAOxQ,IACvBmS,EAAOnS,GAAK,EAIhBuH,KAAKuI,OAAS,IAAIyB,MAAMtB,GACxB,IAAK,IAAIpC,EAAI,EAAGA,EAAIoC,EAAQpC,IACxBtG,KAAKuI,OAAOjC,GAAKsE,EAAOpP,QAc5BwE,KAAKsI,cAAgB,IAAI0B,MAAMhK,KAAK0I,QACpC1I,KAAKqJ,cAAgB,IAAIW,MAAMhK,KAAKiJ,OAEpCjJ,KAAKsI,cAAc,IAAM,EACzBtI,KAAKqJ,cAAc,IAAM,EAEzBrJ,KAAK2J,MAAQV,EAAQP,EAAS,EAC9B1I,KAAKsJ,cAAgB,IAAIU,MAAMhK,KAAK2J,OACpC3J,KAAKuJ,cAAgB,IAAIS,MAAMhK,KAAK2J,OAEpC3J,KAAKmB,iBAAmBnB,KAAK2J,OAGjCrK,EAAQsB,UAAUiK,aAAe,WAC7B,IAMIhG,EAAGxC,EANH7H,EAAYwF,KAAK3G,MAAMmB,UACvBD,EAAcyF,KAAK3G,MAAMkB,YAEzBoP,EAAQnP,EAAUtB,OAClBkH,EAAe7F,EAAYrB,OAG3B4R,EAAU9K,KAAKuI,OAAO,GACtBwC,GAAuC,IAA9B/K,KAAK3G,MAAMiH,gBAA4B,EAAI,EACxD,IAAKuE,EAAI,EAAGA,EAAI8E,EAAO9E,GAAK,EAAG,CAC3B,IAAIzC,EAAW5H,EAAUqK,GACrB1C,EAAWC,EAASD,SACpBJ,EAAOgJ,EAAQ3I,EAASL,KACX,IAAbI,EACA2I,EAAQjG,EAAI,GAAK9C,EAEjB/B,KAAKsK,qBAAqBnI,EAAU0C,EAAI,EAAG9C,GAG/CM,EAAW7H,EAAUqK,GAAG3D,MACxBlB,KAAKsJ,cAAcjH,IAAa,EAChCrC,KAAKuJ,cAAclH,GAAYwC,EAAI,EACnC7E,KAAKqJ,cAAcxE,EAAI,GAAKxC,EAIhC,IADA,IAAI2I,EAAW,EACNrS,EAAI,EAAGA,EAAIyH,EAAczH,GAAK,EAAG,CACtC,IAOIJ,EAAG0S,EAPHjQ,EAAaT,EAAY5B,GAEzBuS,EAAkBlQ,EAAWkG,MACjClB,KAAKsJ,cAAc4B,GAAmBF,EACtChL,KAAKuJ,cAAc2B,IAAoB,EACvClL,KAAKsI,cAAc0C,GAAYE,EAG/B,IAAIC,EAAQnQ,EAAWmQ,MACnBC,EAASD,EAAMjS,OACfmS,EAAMrL,KAAKuI,OAAOyC,KACtB,GAAIhQ,EAAWsQ,aAAc,CACzB,IAAK/S,EAAI,EAAGA,EAAI6S,EAAQ7S,GAAK,EACzB0S,EAAOE,EAAM5S,GAEb8S,EADSrL,KAAKuJ,cAAc0B,EAAK7I,SAASlB,QAC5B+J,EAAK7F,YAGvBiG,EAAI,GAAKrQ,EAAWC,QACjB,CACH,IAAK1C,EAAI,EAAGA,EAAI6S,EAAQ7S,GAAK,EACzB0S,EAAOE,EAAM5S,GAEb8S,EADSrL,KAAKuJ,cAAc0B,EAAK7I,SAASlB,SAC3B+J,EAAK7F,YAGxBiG,EAAI,IAAMrQ,EAAWC,OAOjCqE,EAAQsB,UAAU4E,SAAW,SAAUnM,GAGnC,IAAI4P,GAFJjJ,KAAK3G,MAAQA,GAEKgH,WAAa,EAC3BqI,EAASrP,EAAM+G,aAAe,EAKlC,OAFAJ,KAAK2K,WAAW1B,EAAOP,EAAQrP,EAAMmB,UAAWnB,EAAM8G,uBACtDH,KAAK6K,eACE7K,MAGXV,EAAQsB,UAAUc,mBAAqB,WACnC,GAAmC,EAA/B1B,KAAKgB,iBAAiB9H,OACtB,OAAO8G,KAAKgB,iBAAiBC,MAGjC,IAAIC,EAAQlB,KAAKmB,iBAEjB,OADAnB,KAAKmB,kBAAoB,EAClBD,GAGX5B,EAAQsB,UAAU2K,QAAU,WAIxB,IAHA,IAAIA,EAAU,EAEVhD,EAASvI,KAAKuI,OACTnQ,EAAI,EAAGA,EAAI4H,KAAK0I,OAAQtQ,IAE7B,IADA,IAAIiT,EAAM9C,EAAOnQ,GACRO,EAAI,EAAGA,EAAIqH,KAAKiJ,MAAOtQ,IACb,IAAX0S,EAAI1S,KACJ4S,GAAW,GAKvB,OAAOA,GAAWvL,KAAK0I,OAAS1I,KAAKiJ,QAKzC3J,EAAQsB,UAAU4K,cAAgB,WAE9B,IAAI7C,EAAgBxB,KAAKyB,MAAM,EAAI5I,KAAKF,WACpC6H,EAAa3H,KAAKuI,OAAOvI,KAAKkJ,cAAclJ,KAAKwI,WACjDiD,EACAtE,KAAKyB,OAAOG,OAAOC,QAAUrB,GAAcgB,GAAiBA,EAEhE3I,KAAK2H,WAAa8D,EACQ,IAAtBzL,KAAKoJ,eACLpJ,KAAK0L,iBAAmBD,IAMhCnM,EAAQsB,UAAUwJ,YAAc,WAC5B,IAAIzC,GAA4C,IAA9B3H,KAAK3G,MAAMiH,eACzBN,KAAK2H,YAAc3H,KAAK2H,WAE5B,OAA+C,EAA3C3H,KAAK3G,MAAMkM,8BACJ,IAAImC,EAAa1H,KAAM2H,EAAY3H,KAAK0F,SAAU1F,KAAK4H,QAAS5H,KAAK6H,wBAErE,IAAIJ,EAASzH,KAAM2H,EAAY3H,KAAK0F,SAAU1F,KAAK4H,WAIhE,CAAC+D,oBAAoB,EAAE1D,gBAAgB,IAAI2D,GAAG,CAAC,SAASzT,EAAQD,EAAOD,GAEzE,IAAIqH,EAAUnH,EAAQ,gBAEtBmH,EAAQsB,UAAUyJ,KAAO,WACrB,IAAIA,EAAO,IAAI/K,EAAQU,KAAKF,WAE5BuK,EAAKpB,MAAQjJ,KAAKiJ,MAClBoB,EAAK3B,OAAS1I,KAAK0I,OAEnB2B,EAAKV,MAAQ3J,KAAK2J,MAClBU,EAAKhR,MAAQ2G,KAAK3G,MAIlBgR,EAAK7P,UAAYwF,KAAKxF,UACtB6P,EAAK9I,kBAAoBvB,KAAKuB,kBAC9B8I,EAAKlB,iBAAmBnJ,KAAKmJ,iBAC7BkB,EAAKlJ,iBAAmBnB,KAAKmB,iBAG7BkJ,EAAK/B,cAAgBtI,KAAKsI,cAAc9M,QACxC6O,EAAKhB,cAAgBrJ,KAAKqJ,cAAc7N,QAExC6O,EAAKf,cAAgBtJ,KAAKsJ,cAAc9N,QACxC6O,EAAKd,cAAgBvJ,KAAKuJ,cAAc/N,QAExC6O,EAAKrJ,iBAAmBhB,KAAKgB,iBAAiBxF,QAG9C,IADA,IAAIqQ,EAAyB,GACrBrT,EAAI,EAAGA,EAAIwH,KAAKwJ,mBAAmBtQ,OAAQV,IAC/CqT,EAAuBrT,GAAKwH,KAAKwJ,mBAAmBhR,GAAG6R,OAE3DA,EAAKb,mBAAqBqC,EAK1B,IAFA,IAAItD,EAASvI,KAAKuI,OACduD,EAAa,IAAI9B,MAAMhK,KAAK0I,QACvBtQ,EAAI,EAAGA,EAAI4H,KAAK0I,OAAQtQ,IAC7B0T,EAAW1T,GAAKmQ,EAAOnQ,GAAGoD,QAK9B,OAFA6O,EAAK9B,OAASuD,EAEPzB,GAGX/K,EAAQsB,UAAU+E,KAAO,WACrB3F,KAAK0J,WAAa1J,KAAKqK,QAG3B/K,EAAQsB,UAAUgF,QAAU,WACxB,GAAwB,OAApB5F,KAAK0J,WAAT,CAIA,IAeItR,EAAGO,EAfHgN,EAAO3F,KAAK0J,WACZqC,EAAcpG,EAAK4C,OAevB,IAdAvI,KAAK2J,MAAQhE,EAAKgE,MAClB3J,KAAK3G,MAAQsM,EAAKtM,MAGlB2G,KAAKxF,UAAYmL,EAAKnL,UACtBwF,KAAKuB,kBAAoBoE,EAAKpE,kBAC9BvB,KAAKmJ,iBAAmBxD,EAAKwD,iBAC7BnJ,KAAKmB,iBAAmBwE,EAAKxE,iBAE7BnB,KAAKiJ,MAAQtD,EAAKsD,MAClBjJ,KAAK0I,OAAS/C,EAAK+C,OAIdtQ,EAAI,EAAGA,EAAI4H,KAAK0I,OAAQtQ,GAAK,EAAG,CACjC,IAAI4T,EAAWD,EAAY3T,GACvBiT,EAAMrL,KAAKuI,OAAOnQ,GACtB,IAAKO,EAAI,EAAGA,EAAIqH,KAAKiJ,MAAOtQ,GAAK,EAC7B0S,EAAI1S,GAAKqT,EAASrT,GAK1B,IAAIsT,EAAoBtG,EAAK2C,cAC7B,IAAK3P,EAAI,EAAGA,EAAIqH,KAAK0I,OAAQ/P,GAAK,EAC9BqH,KAAKsI,cAAc3P,GAAKsT,EAAkBtT,GAG9C,KAAOqH,KAAKsI,cAAcpP,OAAS8G,KAAK0I,QACpC1I,KAAKsI,cAAcrH,MAGvB,IAAIiL,EAAuBvG,EAAK0D,cAChC,IAAKjR,EAAI,EAAGA,EAAI4H,KAAKiJ,MAAO7Q,GAAK,EAC7B4H,KAAKqJ,cAAcjR,GAAK8T,EAAqB9T,GAGjD,KAAO4H,KAAKqJ,cAAcnQ,OAAS8G,KAAKiJ,OACpCjJ,KAAKqJ,cAAcpI,MAKvB,IAFA,IAAIkL,EAAYxG,EAAK2D,cACjB8C,EAAYzG,EAAK4D,cACZ1E,EAAI,EAAGA,EAAI7E,KAAK2J,MAAO9E,GAAK,EACjC7E,KAAKsJ,cAAczE,GAAKsH,EAAUtH,GAClC7E,KAAKuJ,cAAc1E,GAAKuH,EAAUvH,GAItC,GAAqC,EAAjCc,EAAK6D,mBAAmBtQ,QAA+C,EAAjC8G,KAAKwJ,mBAAmBtQ,OAAY,CAC1E8G,KAAKwJ,mBAAqB,GAC1BxJ,KAAKqM,6BAA+B,GACpC,IAAI,IAAI7T,EAAI,EAAGA,EAAImN,EAAK6D,mBAAmBtQ,OAAQV,IAAI,CACnD,IAAI8T,EAAwB3G,EAAK6D,mBAAmBhR,GAAG6R,OACvDrK,KAAKwJ,mBAAmBhR,GAAK8T,EAC7BtM,KAAKqM,6BAA6BC,EAAsBnK,UAAYmK,OAK9E,CAACC,eAAe,IAAIC,GAAG,CAAC,SAASrU,EAAQD,EAAOD,GAOlD,IAAIqH,EAAUnH,EAAQ,gBAItB,SAASsU,EAAIC,EAAMrK,EAAUsK,GACzB3M,KAAK0M,KAAOA,EACZ1M,KAAKqC,SAAWA,EAChBrC,KAAK2M,MAAQA,EAKjB,SAASC,EAAOC,EAAmBC,GAC/B9M,KAAK6M,kBAAoBA,EACzB7M,KAAK8M,KAAOA,EAMhB,SAASC,EAAiBlU,EAAG6R,GACzB,OAAOA,EAAEmC,kBAAoBhU,EAAEgU,kBAOnCvN,EAAQsB,UAAUoM,UAAY,SAAUC,GAOpC,GALAjN,KAAK4F,UAEL5F,KAAKkN,kBAAkBD,GACvBjN,KAAKkK,UAEDlK,KAAK3G,MAAMoH,WAEX,IADA,IAAI0M,GAA2B,EACzBA,GAAyB,CAC3B,IAAIC,EAAyBpN,KAAKqN,yBAAwB,GAC1DrN,KAAKsN,eACLtN,KAAKkK,UAMuB,GAAMkD,GAJNpN,KAAKqN,yBAAwB,KAKrDF,GAA2B,KAW3C7N,EAAQsB,UAAUqJ,aAAe,WAC7B,IAAIsD,EAAW,GACXC,EAAa,EACbnJ,EAAYrE,KAAK3G,MAAMgL,UACvBoJ,GAAgB,EAChBC,EAAe,KAUhB1N,KAAK3G,MAAMiL,UAIVoJ,EAAeC,KAAKC,MAAQ5N,KAAK3G,MAAMiL,SAQ3C,IAHA,IAAIuJ,EAAiBC,EAAAA,EACjBC,EAAa,KACbC,EAAoC,GAC/BC,EAAQ,EAAGA,EAAQjO,KAAKwJ,mBAAmBtQ,OAAQ+U,GAAS,EACjED,EAAkC1P,KAAKwP,EAAAA,GAM3C,IACII,EADAC,EAAS,IAAIvB,GAAQkB,EAAAA,EAAU,IAKnC,IAFAP,EAASjP,KAAK6P,GAEW,EAAlBZ,EAASrU,SAAgC,IAAlBuU,GAA0BE,KAAKC,MAAQF,GAiBjE,GAdIQ,EADDlO,KAAK3G,MAAMiH,eACYN,KAAK0L,kBAAoB,EAAIrH,GAE7BrE,KAAK0L,kBAAoB,EAAIrH,GAIvC,EAAZA,GACIwJ,EAAiBK,IACjBT,GAAgB,MAKxBU,EAASZ,EAAStM,OACP4L,kBAAoBgB,GAA/B,CAQA,IAAIf,EAAOqB,EAAOrB,KAIlB,GAHA9M,KAAKgN,UAAUF,GAEfU,KACsB,IAAlBxN,KAAK0F,SAAT,CAIA,IAAIiC,EAAa3H,KAAK2H,WACtB,KAAiBkG,EAAblG,GAAJ,CAMA,GAAIA,IAAekG,EAAe,CAE9B,IADA,IAAIO,GAA2B,EACtB5V,EAAI,EAAGA,EAAIwH,KAAKwJ,mBAAmBtQ,UACpC8G,KAAKwJ,mBAAmBhR,GAAGuR,aAAa,GAAKiE,EAAkCxV,IADnCA,GAAK,EAG9C,GAAIwH,KAAKwJ,mBAAmBhR,GAAGuR,aAAa,GAAKiE,EAAkCxV,GAAI,CAC1F4V,GAA2B,EAC3B,MAIR,GAAIA,EACA,SAKR,IAA0B,IAAtBpO,KAAKqO,aAAuB,CAQ5B,GAHArO,KAAKsO,cAAe,EAGD,IAAfd,EAEA,YADAxN,KAAK6H,uBAAyB2F,GAIlCO,EAAaI,EACbN,EAAiBlG,EACjB,IAAK,IAAI4G,EAAQ,EAAGA,EAAQvO,KAAKwJ,mBAAmBtQ,OAAQqV,GAAS,EACjEP,EAAkCO,GAASvO,KAAKwJ,mBAAmB+E,GAAOxE,aAAa,OAExF,CACgB,IAAfyD,GAGAxN,KAAK2F,OAgDT,IARA,IAAIvD,EAAWpC,KAAKwO,uBAEhBnM,EAAWD,EAASlB,MAEpBuN,EAAW,GACXC,EAAU,GAEVC,EAAQ7B,EAAK5T,OACRP,EAAI,EAAGA,EAAIgW,EAAOhW,GAAK,EAAG,CAC/B,IAAIiW,EAAM9B,EAAKnU,GACXiW,EAAIvM,WAAaA,EACA,QAAbuM,EAAIlC,KACJgC,EAAQpQ,KAAKsQ,GAEbH,EAASnQ,KAAKsQ,IAGlBH,EAASnQ,KAAKsQ,GACdF,EAAQpQ,KAAKsQ,IAIrB,IAAItS,EAAM6K,KAAK0H,KAAKzM,EAASuK,OACzBtQ,EAAM8K,KAAK2H,MAAM1M,EAASuK,OAE1BoC,EAAU,IAAItC,EAAI,MAAOpK,EAAU/F,GACvCmS,EAASnQ,KAAKyQ,GAEd,IAAIC,EAAS,IAAIvC,EAAI,MAAOpK,EAAUhG,GACtCqS,EAAQpQ,KAAK0Q,GAEbzB,EAASjP,KAAK,IAAIsO,EAAOjF,EAAY8G,IACrClB,EAASjP,KAAK,IAAIsO,EAAOjF,EAAY+G,IAKrCnB,EAAS9C,KAAKsC,MAKH,OAAfgB,GAEA/N,KAAKgN,UAAUe,EAAWjB,MAE9B9M,KAAK6H,uBAAyB2F,IAGhC,CAACjB,eAAe,IAAI0C,GAAG,CAAC,SAAS9W,EAAQD,EAAOD,GAElD,IAAIqH,EAAUnH,EAAQ,gBAEtB,SAAS+W,EAAahO,EAAOyL,GACzB3M,KAAKkB,MAAQA,EACblB,KAAK2M,MAAQA,EAKjBrN,EAAQsB,UAAU4N,qBAAuB,WAQrC,IAPA,IAAIW,EAAkB,EAClBC,EAAmB,KACnBC,EAAmB,KAGnBnP,EAAmBF,KAAK3G,MAAM6G,iBAC9BoP,EAAepP,EAAiBhH,OAC3B2L,EAAI,EAAGA,EAAIyK,EAAczK,IAAK,CACnC,IAAIxC,EAAWnC,EAAiB2E,GAAG3D,MAC/BqO,EAASvP,KAAKsJ,cAAcjH,GAChC,IAAgB,IAAZkN,EAAJ,CAIA,IAAIzG,EAAW9I,KAAKuI,OAAOgH,GAAQvP,KAAKwI,WACpCgH,EAAWrI,KAAKsI,IAAI3G,EAAW3B,KAAKyB,MAAME,IAC1CqG,EAAkBK,IAClBL,EAAkBK,EAClBJ,EAAmB/M,EACnBgN,EAAmBvG,IAI3B,OAAO,IAAIoG,EAAaE,EAAkBC,IAK9C/P,EAAQsB,UAAU8O,+BAAiC,WAO/C,IANA,IAAIC,EAAc7B,EAAAA,EACdsB,EAAmB,KACnBC,EAAmB,KAEnBnP,EAAmBF,KAAK3G,MAAM6G,iBAC9BoP,EAAepP,EAAiBhH,OAC3B2L,EAAI,EAAGA,EAAIyK,EAAczK,IAAK,CACnC,IAAIzC,EAAWlC,EAAiB2E,GAC5BxC,EAAWD,EAASlB,MACpBqO,EAASvP,KAAKsJ,cAAcjH,GAChC,IAAgB,IAAZkN,EAAJ,CAMA,IAAIzG,EAAW9I,KAAKuI,OAAOgH,GAAQvP,KAAKwI,WACxC,GAAIrB,KAAKsI,IAAI3G,EAAW3B,KAAKyB,MAAME,IAAa9I,KAAKF,UAAW,CAC5D,IAAIiC,EAAOK,EAASL,KACFA,EAAd4N,IACAA,EAAc5N,EACdqN,EAAmB/M,EACnBgN,EAAmBvG,KAK/B,OAAO,IAAIoG,EAAaE,EAAkBC,KAG5C,CAAC9C,eAAe,IAAIqD,GAAG,CAAC,SAASzX,EAAQD,EAAOD,GAElD,IAAIqH,EAAUnH,EAAQ,gBAClB0X,EAAgB1X,EAAQ,qBAAqB0X,cAEjDvQ,EAAQsB,UAAUsM,kBAAoB,SAAU4C,GAO5C,IANA,IAgBInX,EAhBAoX,EAAkBD,EAAe5W,OAEjCwP,EAAS1I,KAAK0I,OACdsH,EAAiBtH,EAASqH,EAGrBE,EAAIvH,EAAQuH,EAAID,EAAgBC,GAAK,OACnBhM,IAAnBjE,KAAKuI,OAAO0H,KACZjQ,KAAKuI,OAAO0H,GAAKjQ,KAAKuI,OAAO0H,EAAI,GAAGzU,SAK5CwE,KAAK0I,OAASsH,EACdhQ,KAAK2J,MAAQ3J,KAAKiJ,MAAQjJ,KAAK0I,OAAS,EAIxC,IADA,IAAIwH,EAAalQ,KAAKiJ,MAAQ,EACrBxQ,EAAI,EAAGA,EAAIsX,EAAiBtX,GAAK,EAAG,CACzC,IAAImW,EAAMkB,EAAerX,GAGrBL,EAAIsQ,EAASjQ,EAEb0X,EAAqB,QAAbvB,EAAIlC,MAAmB,EAAI,EAGnCrK,EAAWuM,EAAIvM,SACf+N,EAAcpQ,KAAKsJ,cAAcjH,GACjCgO,EAAgBrQ,KAAKuI,OAAOnQ,GAChC,IAAqB,IAAjBgY,EAAoB,CAGpB,IADAC,EAAcrQ,KAAKwI,WAAa2H,EAAOvB,EAAIjC,MACtChU,EAAI,EAAGA,GAAKuX,EAAYvX,GAAK,EAC9B0X,EAAc1X,GAAK,EAEvB0X,EAAcrQ,KAAKuJ,cAAclH,IAAa8N,MAC3C,CAEH,IAAIZ,EAASvP,KAAKuI,OAAO6H,GACrBtH,EAAWyG,EAAOvP,KAAKwI,WAE3B,IADA6H,EAAcrQ,KAAKwI,WAAa2H,GAAQvB,EAAIjC,MAAQ7D,GAC/CnQ,EAAI,EAAGA,GAAKuX,EAAYvX,GAAK,EAC9B0X,EAAc1X,IAAMwX,EAAOZ,EAAO5W,GAK1C,IAAI2X,EAAgBtQ,KAAK0B,qBACzB1B,KAAKsI,cAAclQ,GAAKkY,EACxBtQ,KAAKsJ,cAAcgH,GAAiBlY,EACpC4H,KAAKuJ,cAAc+G,IAAkB,EACrCtQ,KAAKuB,kBAAkB+O,GAAiB,IAAIT,EAAc,IAAIS,EAAeA,GAC7EtQ,KAAK2J,OAAS,IAItBrK,EAAQsB,UAAU2P,qBAAuB,SAASvF,GAEjD,GAAGA,IAAahL,KAAKkJ,aAEpB,OAAO,EAGIlJ,KAAK3G,MAAjB,IACIkP,EAASvI,KAAKuI,OAGlB,IADavI,KAAKuB,kBAAkBvB,KAAKsI,cAAc0C,IAC3C/I,UACX,OAAO,EAGR,IAAI3G,EAAIiN,EAAOyC,GAAUhL,KAAKwI,WAC1BgI,EAASlV,EAAI6L,KAAK2H,MAAMxT,GAE5B,GAAIkV,EAASxQ,KAAKF,WAAa,EAAIE,KAAKF,UAAY0Q,EACnD,OAAO,EAIR,IAAIpY,EAAI4H,KAAK0I,OACbH,EAAOnQ,GAAKmQ,EAAOnQ,EAAI,GAAGoD,QAC1BwE,KAAK0I,QAAU,EAGf1I,KAAK2J,OAAS,EACd,IAAI2G,EAAgBtQ,KAAK0B,qBACzB1B,KAAKsI,cAAclQ,GAAKkY,EACxBtQ,KAAKsJ,cAAcgH,GAAiBlY,EACpC4H,KAAKuJ,cAAc+G,IAAkB,EACrCtQ,KAAKuB,kBAAkB+O,GAAiB,IAAIT,EAAc,IAAIS,EAAeA,GAE7E/H,EAAOnQ,GAAG4H,KAAKwI,WAAarB,KAAK2H,MAAMxT,GAEvC,IAAK,IAAImV,EAAW,EAAGA,EAAWzQ,KAAKqJ,cAAcnQ,OAAQuX,GAAY,EAAG,CAG3E,GAFezQ,KAAKuB,kBAAkBvB,KAAKqJ,cAAcoH,IAE3CxO,UAEP,CACN,IAAIyO,EAAOnI,EAAOyC,GAAUyF,GACxBE,EAAYxJ,KAAK2H,MAAM4B,GAAMvJ,KAAK9K,IAAI,EAAGqU,EAAOvJ,KAAK2H,MAAM4B,GAAQF,IAAW,EAAIA,GACtFjI,EAAOnQ,GAAGqY,GAAYE,OAJtBpI,EAAOnQ,GAAGqY,GAAYtJ,KAAK7K,IAAI,EAAGiM,EAAOyC,GAAUyF,IAAa,EAAID,IAQtE,IAAI,IAAI7X,EAAI,EAAGA,EAAIqH,KAAKiJ,MAAOtQ,GAAK,EACnC4P,EAAOnQ,GAAGO,IAAM4P,EAAOyC,GAAUrS,GAGlC,OAAO,GAGR2G,EAAQsB,UAAUgQ,qBAAuB,SAAS5F,GAEjD,GAAIA,IAAahL,KAAKkJ,aAErB,OAAO,EAGIlJ,KAAK3G,MAAjB,IACIkP,EAASvI,KAAKuI,OAGlB,IADavI,KAAKuB,kBAAkBvB,KAAKsI,cAAc0C,IAC3C/I,UACX,OAAO,EAGR,IAAIyI,EAAInC,EAAOyC,GAAUhL,KAAKwI,WAC1B9P,EAAIgS,EAAIvD,KAAK2H,MAAMpE,GAEvB,GAAIhS,EAAIsH,KAAKF,WAAa,EAAIE,KAAKF,UAAYpH,EAC9C,OAAO,EAIR,IAAIN,EAAI4H,KAAK0I,OACbH,EAAOnQ,GAAKmQ,EAAOnQ,EAAI,GAAGoD,QAC1BwE,KAAK0I,QAAU,EAIf1I,KAAK2J,OAAS,EACd,IAAI2G,EAAgBtQ,KAAK0B,qBACzB1B,KAAKsI,cAAclQ,GAAKkY,EACxBtQ,KAAKsJ,cAAcgH,GAAiBlY,EACpC4H,KAAKuJ,cAAc+G,IAAkB,EACrCtQ,KAAKuB,kBAAkB+O,GAAiB,IAAIT,EAAc,IAAIS,EAAeA,GAE7E/H,EAAOnQ,GAAG4H,KAAKwI,YAAc9P,EAG7B,IAAI,IAAI+X,EAAW,EAAGA,EAAWzQ,KAAKqJ,cAAcnQ,OAAQuX,GAAY,EAAG,CAC1E,IAAIrO,EAAWpC,KAAKuB,kBAAkBvB,KAAKqJ,cAAcoH,IAErDI,EAAKtI,EAAOyC,GAAUyF,GACtBK,EAAKD,EAAK1J,KAAK2H,MAAM+B,GAEtBzO,EAASH,UAEVsG,EAAOnQ,GAAGqY,GADRK,GAAMpY,GACeoY,IAEC,EAAIA,GAAMpY,EAAIoY,EAItCvI,EAAOnQ,GAAGqY,GADD,GAANI,GACoBA,EAEDA,EAAKnY,GAAK,EAAIA,GAKvC,OAAO,GAUR4G,EAAQsB,UAAU0M,aAAe,cAe/B,CAACyD,oBAAoB,GAAGxE,eAAe,IAAIyE,GAAG,CAAC,SAAS7Y,EAAQD,EAAOD,GAGzE,IAAIqH,EAAUnH,EAAQ,gBAItBmH,EAAQsB,UAAUqQ,WAAa,SAAU5O,GAErC,IAAIjK,EAAI4H,KAAKsJ,cAAcjH,GAC3B,IAAW,IAAPjK,EAAU,CAOV,IAJA,IAAIO,EAAIqH,KAAKuJ,cAAclH,GAIlB6O,EAAK,EAAGA,EAAKlR,KAAK0I,OAAQwI,GAAM,EAAG,CACxC,IAAI9L,EAAcpF,KAAKuI,OAAO2I,GAAIvY,GAClC,GAAIyM,GAAepF,KAAKF,WAAaE,KAAKF,UAAYsF,EAAa,CAC/DhN,EAAI8Y,EACJ,OAIRlR,KAAKmR,MAAM/Y,EAAGO,GAGlB,OAAOP,GAGXkH,EAAQsB,UAAUwQ,eAAiB,SAAU/O,GAEzC,IAAI1J,EAAIqH,KAAKuJ,cAAclH,GAC3B,IAAW,IAAP1J,EAAU,CAQV,IALA,IAAIP,EAAI4H,KAAKsJ,cAAcjH,GAIvBgP,EAAWrR,KAAKuI,OAAOnQ,GAClBkZ,EAAK,EAAGA,EAAKtR,KAAK0I,OAAQ4I,GAAM,EAAG,CACxC,IAAIlM,EAAciM,EAASC,GAC3B,GAAIlM,GAAepF,KAAKF,WAAaE,KAAKF,UAAYsF,EAAa,CAC/DzM,EAAI2Y,EACJ,OAIRtR,KAAKmR,MAAM/Y,EAAGO,GAGlB,OAAOA,GAGX2G,EAAQsB,UAAUuJ,qBAAuB,WAGrC,IAFA,IAAIR,EAAQ3J,KAAKxF,UAAUtB,OACvByP,EAAgBxB,KAAKyB,MAAM,EAAI5I,KAAKF,WAC/B+E,EAAI,EAAGA,EAAI8E,EAAO9E,GAAK,EAAG,CAC/B,IAAIzC,EAAWpC,KAAKxF,UAAUqK,GAC1BxC,EAAWD,EAASlB,MAEpB9I,EAAI4H,KAAKsJ,cAAcjH,GAC3B,IAAW,IAAPjK,EAEAgK,EAASuK,MAAQ,MACd,CAEH,IAAI7D,EAAW9I,KAAKuI,OAAOnQ,GAAG4H,KAAKwI,WACnCpG,EAASuK,MAAQxF,KAAKyB,OAAOE,EAAWC,OAAOC,SAAWL,GAAiBA,KAKvFrJ,EAAQsB,UAAUqC,oBAAsB,SAAUjI,EAAYkI,GAE1D,IAAIuF,EAAUzI,KAAK0I,OAAS,EACxB2H,EAAgBrQ,KAAKsJ,cAActO,EAAWkG,OAClD,IAAuB,IAAnBmP,EAAsB,CAKtB,IAHA,IAAIkB,EAAcvR,KAAKuJ,cAAcvO,EAAWkG,OAGvC9I,EAAI,EAAGA,GAAKqQ,EAASrQ,GAAK,EAAG,CAClC,IAAIiT,EAAMrL,KAAKuI,OAAOnQ,GACtBiT,EAAIrL,KAAKwI,YAActF,EAAamI,EAAIkG,GAG5C,IAAIC,EAAsBxR,KAAKwJ,mBAAmBtQ,OAClD,GAA0B,EAAtBsY,EACA,IAAK,IAAIhZ,EAAI,EAAGA,EAAIgZ,EAAqBhZ,GAAK,EAAG,CAC7C,IAAIuR,EAAe/J,KAAKwJ,mBAAmBhR,GAAGuR,aAC9CA,EAAa/J,KAAKwI,YAActF,EAAa6G,EAAawH,SAMlEvR,KAAKuI,OAAO8H,GAAerQ,KAAKwI,YAActF,GAItD5D,EAAQsB,UAAUuC,4BAA8B,SAAUnI,EAAYoH,EAAUc,GAE5E,GAAIlI,EAAWkG,QAAUkB,EAASlB,MAC9B,MAAM,IAAIpI,MAAM,kGAGpB,IAAIV,EAAI4H,KAAKiR,WAAWjW,EAAWkG,OAE/BuQ,EAASzR,KAAKuJ,cAAcnH,EAASlB,OACzC,IAAgB,IAAZuQ,EAEA,IADA,IAAIC,EAAS1R,KAAKsJ,cAAclH,EAASlB,OAChCvI,EAAI,EAAGA,EAAIqH,KAAKiJ,MAAOtQ,GAAK,EACjCqH,KAAKuI,OAAOnQ,GAAGO,IAAMuK,EAAalD,KAAKuI,OAAOmJ,GAAQ/Y,QAG1DqH,KAAKuI,OAAOnQ,GAAGqZ,IAAWvO,GAIlC5D,EAAQsB,UAAUyC,WAAa,SAAUjB,EAAUc,GAE/C,IAAIb,EAAWD,EAASlB,MACpBgP,EAAalQ,KAAKiJ,MAAQ,EAC1B0I,EAAY3R,KAAKuJ,cAAclH,GACnC,IAAmB,IAAfsP,EAAkB,CAElB,IAEIhZ,EAFAiZ,EAAc5R,KAAKuI,OAAOvI,KAAKsJ,cAAcjH,IAGjD,GAA0B,IAAtBD,EAASD,SAAgB,CACzB,IAAI2I,EAAU9K,KAAKuI,OAAO,GAG1B,IAAK5P,EAAI,EAAGA,GAAKuX,EAAYvX,GAAK,EAC9BmS,EAAQnS,IAAMuK,EAAa0O,EAAYjZ,OAExC,CACH,IAAIoR,EAAe/J,KAAKyJ,qBAAqBrH,EAASD,UAAU4H,aAChE,IAAKpR,EAAI,EAAGA,GAAKuX,EAAYvX,GAAK,EAC9BoR,EAAapR,IAAMuK,EAAa0O,EAAYjZ,SAMpDqH,KAAKuI,OAAO,GAAGoJ,IAAczO,GAIrC5D,EAAQsB,UAAUY,cAAgB,SAAUxG,GAExC,IAAImV,EAAOnV,EAAWsQ,aAAe,GAAK,EACtC7C,EAAUzI,KAAK0I,OAEf2H,EAAgBrQ,KAAKuI,OAAOE,QACVxE,IAAlBoM,IACAA,EAAgBrQ,KAAKuI,OAAO,GAAG/M,QAC/BwE,KAAKuI,OAAOE,GAAW4H,GAK3B,IADA,IAAIH,EAAalQ,KAAKiJ,MAAQ,EACrBtQ,EAAI,EAAGA,GAAKuX,EAAYvX,GAAK,EAClC0X,EAAc1X,GAAK,EAIvB0X,EAAcrQ,KAAKwI,WAAa2H,EAAOnV,EAAWC,IAIlD,IAFA,IAAIkQ,EAAQnQ,EAAWmQ,MACnBC,EAASD,EAAMjS,OACVX,EAAI,EAAGA,EAAI6S,EAAQ7S,GAAK,EAAG,CAChC,IAAI0S,EAAOE,EAAM5S,GACb6M,EAAc6F,EAAK7F,YACnB/C,EAAW4I,EAAK7I,SAASlB,MAEzBkP,EAAcpQ,KAAKsJ,cAAcjH,GACrC,IAAqB,IAAjB+N,EAEAC,EAAcrQ,KAAKuJ,cAAclH,KAAc8N,EAAO/K,MACnD,CAEH,IAAImK,EAASvP,KAAKuI,OAAO6H,GACVb,EAAOvP,KAAKwI,WAC3B,IAAK7P,EAAI,EAAGA,GAAKuX,EAAYvX,GAAK,EAC9B0X,EAAc1X,IAAMwX,EAAO/K,EAAcmK,EAAO5W,IAK5D,IAAIkZ,EAAa7W,EAAWkG,MAC5BlB,KAAKsI,cAAcG,GAAWoJ,EAC9B7R,KAAKsJ,cAAcuI,GAAcpJ,EACjCzI,KAAKuJ,cAAcsI,IAAe,EAElC7R,KAAK0I,QAAU,GAGnBpJ,EAAQsB,UAAU6B,iBAAmB,SAAUzH,GAC3C,IAAI6W,EAAa7W,EAAWkG,MACxBuH,EAAUzI,KAAK0I,OAAS,EAGxBtQ,EAAI4H,KAAKiR,WAAWY,GAKpBjH,EAAS5K,KAAKuI,OAAOE,GACzBzI,KAAKuI,OAAOE,GAAWzI,KAAKuI,OAAOnQ,GACnC4H,KAAKuI,OAAOnQ,GAAKwS,EAGjB5K,KAAKsI,cAAclQ,GAAK4H,KAAKsI,cAAcG,GAC3CzI,KAAKsI,cAAcG,IAAY,EAC/BzI,KAAKsJ,cAAcuI,IAAe,EAGlC7R,KAAKgB,iBAAiBhB,KAAKgB,iBAAiB9H,QAAU2Y,EAEtD7W,EAAWsG,MAAMJ,OAAS,EAE1BlB,KAAK0I,QAAU,GAGnBpJ,EAAQsB,UAAUkB,YAAc,SAAUM,GAItC,IAAIqG,EAAUzI,KAAK0I,OAAS,EACxBwH,EAAalQ,KAAKiJ,MAClBlH,GAAqC,IAA9B/B,KAAK3G,MAAMiH,gBAA2B8B,EAASL,KAAOK,EAASL,KACtEI,EAAWC,EAASD,SAGpBqP,EAAsBxR,KAAKwJ,mBAAmBtQ,OAClD,GAA0B,EAAtBsY,EACA,IAAK,IAAIhZ,EAAI,EAAGA,EAAIgZ,EAAqBhZ,GAAK,EAC1CwH,KAAKwJ,mBAAmBhR,GAAGuR,aAAamG,GAAc,EAI7C,IAAb/N,EACAnC,KAAKuI,OAAO,GAAG2H,GAAcnO,GAE7B/B,KAAKsK,qBAAqBnI,EAAU+N,EAAYnO,GAChD/B,KAAKuI,OAAO,GAAG2H,GAAc,GAIjC,IAAK,IAAI9X,EAAI,EAAGA,GAAKqQ,EAASrQ,GAAK,EAC/B4H,KAAKuI,OAAOnQ,GAAG8X,GAAc,EAIjC,IAAI7N,EAAWD,EAASlB,MACxBlB,KAAKqJ,cAAc6G,GAAc7N,EAEjCrC,KAAKsJ,cAAcjH,IAAa,EAChCrC,KAAKuJ,cAAclH,GAAY6N,EAE/BlQ,KAAKiJ,OAAS,GAIlB3J,EAAQsB,UAAU+B,eAAiB,SAAUP,GACzC,IAAIC,EAAWD,EAASlB,MAGpBvI,EAAIqH,KAAKoR,eAAe/O,GACxB6N,EAAalQ,KAAKiJ,MAAQ,EAC9B,GAAItQ,IAAMuX,EAAY,CAElB,IADA,IAAIzH,EAAUzI,KAAK0I,OAAS,EACnBtQ,EAAI,EAAGA,GAAKqQ,EAASrQ,GAAK,EAAG,CAClC,IAAIiT,EAAMrL,KAAKuI,OAAOnQ,GACtBiT,EAAI1S,GAAK0S,EAAI6E,GAGjB,IAAIsB,EAAsBxR,KAAKwJ,mBAAmBtQ,OAClD,GAA0B,EAAtBsY,EACA,IAAK,IAAIhZ,EAAI,EAAGA,EAAIgZ,EAAqBhZ,GAAK,EAAG,CAC7C,IAAIuR,EAAe/J,KAAKwJ,mBAAmBhR,GAAGuR,aAC9CA,EAAapR,GAAKoR,EAAamG,GAIvC,IAAI4B,EAAiB9R,KAAKqJ,cAAc6G,GACxClQ,KAAKqJ,cAAc1Q,GAAKmZ,EACxB9R,KAAKuJ,cAAcuI,GAAkBnZ,EAIzCqH,KAAKqJ,cAAc6G,IAAe,EAClClQ,KAAKuJ,cAAclH,IAAa,EAGhCrC,KAAKgB,iBAAiBhB,KAAKgB,iBAAiB9H,QAAUmJ,EAEtDD,EAASlB,OAAS,EAElBlB,KAAKiJ,OAAS,IAGhB,CAACsD,eAAe,IAAIwF,GAAG,CAAC,SAAS5Z,EAAQD,EAAOD,GAGlDE,EAAQ,gBACRA,EAAQ,0BACRA,EAAQ,4BACRA,EAAQ,YACRA,EAAQ,eACRA,EAAQ,4BACRA,EAAQ,0BAERD,EAAOD,QAAUE,EAAQ,iBAEvB,CAACoU,eAAe,EAAEyF,cAAc,GAAGC,2BAA2B,GAAGC,yBAAyB,GAAGC,2BAA2B,GAAGC,yBAAyB,GAAGC,WAAW,GAAGC,eAAe,KAAKC,GAAG,CAAC,SAASpa,EAAQD,EAAOD,GAEvN,IAAIqH,EAAUnH,EAAQ,gBAEtBmH,EAAQsB,UAAU4R,mBAAqB,WAEnC,IADA,IAAIC,EAAQ,EACHra,EAAI,EAAGA,EAAI4H,KAAK0I,OAAQtQ,GAAK,EAClC,GAAI4H,KAAKuB,kBAAkBvB,KAAKsI,cAAclQ,IAAI6J,UAAW,CACzD,IAAIyQ,EAAc1S,KAAKuI,OAAOnQ,GAAG4H,KAAKwI,YACtCkK,GAA4BvL,KAAK2H,MAAM4D,IACrB1S,KAAKF,YAAc4S,EAAc1S,KAAKF,YACpD2S,GAAS,GAKrB,OAAOA,GAKXnT,EAAQsB,UAAUyN,WAAa,WAG3B,IAFA,IAAInO,EAAmBF,KAAK3G,MAAM6G,iBAC9BoP,EAAepP,EAAiBhH,OAC3B2L,EAAI,EAAGA,EAAIyK,EAAczK,IAAK,CACnC,IAAI0K,EAASvP,KAAKsJ,cAAcpJ,EAAiB2E,GAAG3D,OACpD,IAAgB,IAAZqO,EAAJ,CAIA,IAAIzG,EAAW9I,KAAKuI,OAAOgH,GAAQvP,KAAKwI,WACxC,GAAIrB,KAAKsI,IAAI3G,EAAW3B,KAAKyB,MAAME,IAAa9I,KAAKF,UACjD,OAAO,GAGf,OAAO,GAIXR,EAAQsB,UAAUyM,wBAA0B,SAASsF,GAyBjD,IAxBA,IAAIC,GAAU,EAwBLxa,EAAI,EAAGA,EAAI4H,KAAK0I,OAAQtQ,GAAK,EAClC,GAAI4H,KAAKuB,kBAAkBvB,KAAKsI,cAAclQ,IAAI6J,UAAW,CACzD,IAAIhH,EAAM+E,KAAKuI,OAAOnQ,GAAG4H,KAAKwI,WAG9B,GAFAvN,EAAMkM,KAAKsI,IAAIxU,GACGkM,KAAK7K,IAAIrB,EAAMkM,KAAK2H,MAAM7T,GAAMkM,KAAK2H,MAAM7T,EAAM,IACjD+E,KAAKF,WACnB,IAAK6S,EACD,OAAO,OAGK,IAAZC,EACAA,EAAS3X,EAET2X,GAAU3X,EAM1B,OAAgB,IAAZ2X,EACO,EAEJA,IAGT,CAACrG,eAAe,IAAIsG,GAAG,CAAC,SAAS1a,EAAQD,EAAOD,GAGpCE,EAAQ,gBAOdyI,UAAUoF,IAAM,SAAUC,EAAS6M,GAKvClQ,QAAQoD,IAAI,OAAQC,EAAS,QAC7BrD,QAAQoD,IAAI,eAAgBhG,KAAKiJ,MAAQ,GACzCrG,QAAQoD,IAAI,iBAAkBhG,KAAK0I,OAAS,GAE5C9F,QAAQoD,IAAI,gBAAiBhG,KAAKsI,eAClC1F,QAAQoD,IAAI,oBAAqBhG,KAAKqJ,eACtCzG,QAAQoD,IAAI,OAAQhG,KAAKsJ,eACzB1G,QAAQoD,IAAI,OAAQhG,KAAKuJ,eAEzB,IAKIjD,EACA3N,EAEAP,EACAgK,EACAC,EACA0Q,EACAC,EAEAC,EACAC,EAEA7H,EACA8H,EAfAC,EAAmB,GACnBC,EAAiB,CAAC,KAgBtB,IAAK1a,EAAI,EAAGA,EAAIqH,KAAKiJ,MAAOtQ,GAAK,EAC7B0J,EAAWrC,KAAKqJ,cAAc1Q,GAQ9Bqa,GALID,OADa9O,KADjB7B,EAAWpC,KAAKuB,kBAAkBc,IAEpB,IAAMA,EAEND,EAASJ,IAGC9I,OACdiO,KAAKsI,IAAIuD,EAAgB,GACnCC,EAAa,IACbC,EAAY,KAeQ,EAAhBF,EACAC,GAAc,IAEdC,GAAa,KAGjBG,EAAe1a,GAAKsa,EAEpBG,GAAoBF,EAAYH,EAEpCnQ,QAAQoD,IAAIoN,GAKZ,IAAIE,EAAWtT,KAAKuI,OAAOvI,KAAKkJ,cAC5BqK,EAAiB,KAerB,IAAKjN,EAAI,EAAGA,EAAItG,KAAKiJ,MAAO3C,GAAK,EAE7BiN,GADY,KAEZA,GAAkBF,EAAe/M,GACjCiN,GAAkBD,EAAShN,GAAGkN,QAlFb,GA2FrB,IANAD,GADY,KACkBF,EAAe,GACzCC,EAAS,GAAGE,QAtFK,GAuFrB5Q,QAAQoD,IAAIuN,EAAiB,OAIxBnb,EAAI,EAAGA,EAAI4H,KAAK0I,OAAQtQ,GAAK,EAAG,CAajC,IAZAiT,EAAMrL,KAAKuI,OAAOnQ,GAClB+a,EAAY,KAWPxa,EAAI,EAAGA,EAAIqH,KAAKiJ,MAAOtQ,GAAK,EAE7Bwa,GADY,KACaE,EAAe1a,GAAK0S,EAAI1S,GAAG6a,QA1GvC,GA6GjBL,GADY,KACaE,EAAe,GAAKhI,EAAI,GAAGmI,QA7GnC,GAgHjBnR,EAAWrC,KAAKsI,cAAclQ,GAG1B2a,OADa9O,KADjB7B,EAAWpC,KAAKuB,kBAAkBc,IAEpB,IAAMA,EAEND,EAASJ,GAEvBY,QAAQoD,IAAImN,EAAY,KAAOJ,GAEnCnQ,QAAQoD,IAAI,IAGZ,IAAIwL,EAAsBxR,KAAKwJ,mBAAmBtQ,OAClD,GAA0B,EAAtBsY,EAAyB,CACzB5O,QAAQoD,IAAI,4BACZ,IAAK,IAAIxN,EAAI,EAAGA,EAAIgZ,EAAqBhZ,GAAK,EAAG,CAC7C,IAAIuR,EAAe/J,KAAKwJ,mBAAmBhR,GAAGuR,aAC1C0J,EAAqB,GACzB,IAAKnN,EAAI,EAAGA,EAAItG,KAAKiJ,MAAO3C,GAAK,EAE7BmN,GADY1J,EAAazD,GAAK,EAAI,GAAK,IAEvCmN,GAAsBJ,EAAe/M,GACrCmN,GAAsB1J,EAAazD,GAAGkN,QAtI7B,GAyIbC,IADY1J,EAAa,GAAK,EAAI,GAAK,KACLsJ,EAAe,GAC7CtJ,EAAa,GAAGyJ,QA1IP,GA2Ib5Q,QAAQoD,IAAIyN,EAAqB,KAAOjb,IAMhD,OAHAoK,QAAQoD,IAAI,YAAahG,KAAK0F,UAC9B9C,QAAQoD,IAAI,aAAchG,KAAK2H,YAExB3H,OAGT,CAACuM,eAAe,IAAImH,GAAG,CAAC,SAASvb,EAAQD,EAAOD,GAQlD,IAAIqH,EAAUnH,EAAQ,gBAMtBmH,EAAQsB,UAAUsJ,QAAU,WAcxB,OAZAlK,KAAK4H,SAAU,EAGf5H,KAAK2T,UAGiB,IAAlB3T,KAAK0F,UAGL1F,KAAK4T,SAGF5T,MAUXV,EAAQsB,UAAU+S,OAAS,WAYvB,IAXA,IAAI5N,EAAsB/F,KAAK3G,MAAMqH,eACjCmT,EAAkB,GAElBtL,EAASvI,KAAKuI,OACdC,EAAYxI,KAAKwI,UACjB0H,EAAalQ,KAAKiJ,MAAQ,EAC1BR,EAAUzI,KAAK0I,OAAS,EAGxB8E,EAAa,IAEJ,CAUT,IAFA,IAAIsG,EAAkB,EAClBC,GAAY/T,KAAKF,UACZ1H,EAAI,EAAGA,GAAKqQ,EAASrQ,IAAK,EACiC,IAAjD4H,KAAKmJ,iBAAiBnJ,KAAKsI,cAAclQ,IASxD,IAAIuU,EAAQpE,EAAOnQ,GAAGoQ,GAClBmE,EAAQoH,IACRA,EAAWpH,EACXmH,EAAkB1b,GAK1B,GAAwB,IAApB0b,EAGA,OADA9T,KAAK0F,UAAW,EACT8H,EAcX,IAJA,IAAIwG,EAAiB,EACjBC,GAAenG,EAAAA,EACfhD,EAAUvC,EAAO,GACjB2L,EAAa3L,EAAOuL,GACfnb,EAAI,EAAGA,GAAKuX,EAAYvX,IAAK,CAClC,IAAIyM,EAAc8O,EAAWvb,GAU7B,IADgE,IAAjDqH,KAAKmJ,iBAAiBnJ,KAAKqJ,cAAc1Q,KACpCyM,GAAepF,KAAKF,UAAW,CAC/C,IAAIqU,GAAYrJ,EAAQnS,GAAKyM,EACzB6O,EAAcE,IACdF,EAAcE,EACdH,EAAiBrb,IAK7B,GAAuB,IAAnBqb,EAGA,OADAhU,KAAK0F,UAAW,EACT8H,EAGX,GAAGzH,EAAoB,CACnB8N,EAAgBvV,KAAK,CAAC0B,KAAKsI,cAAcwL,GAAkB9T,KAAKqJ,cAAc2K,KAE9E,IAAII,EAAYpU,KAAKU,eAAemT,GACpC,GAAsB,EAAnBO,EAAUlb,OAOT,OALA8G,KAAK3G,MAAMsH,SAASrC,KAAK,oBACzB0B,KAAK3G,MAAMsH,SAASrC,KAAK,UAAW8V,EAAU,IAC9CpU,KAAK3G,MAAMsH,SAASrC,KAAK,WAAY8V,EAAU,IAE/CpU,KAAK0F,UAAW,EACT8H,EAKfxN,KAAKmR,MAAM2C,EAAiBE,GAC5BxG,GAAc,IAStBlO,EAAQsB,UAAUgT,OAAS,WAgBvB,IAfA,IAaIS,EAAapY,EAbb8J,EAAsB/F,KAAK3G,MAAMqH,eACjCmT,EAAkB,GAElBtL,EAASvI,KAAKuI,OACdC,EAAYxI,KAAKwI,UACjB0H,EAAalQ,KAAKiJ,MAAQ,EAC1BR,EAAUzI,KAAK0I,OAAS,EAExB5I,EAAYE,KAAKF,UACjB0R,EAAsBxR,KAAKwJ,mBAAmBtQ,OAC9Cob,EAAuB,KAEvB9G,EAAa,IAGJ,CACT,IAAI1C,EAAUvC,EAAOvI,KAAKkJ,cAGA,EAAtBsI,IACA8C,EAAuB,IAM3B,IAHA,IAAIN,EAAiB,EACjBO,EAAgBzU,EAChB0U,GAAwB,EACnB7b,EAAI,EAAGA,GAAKuX,EAAYvX,IAC7B0b,EAAcvJ,EAAQnS,GACtBsD,GAAgE,IAAjD+D,KAAKmJ,iBAAiBnJ,KAAKqJ,cAAc1Q,IAE9B,EAAtB6Y,IAA4B1R,EAAYuU,GAAeA,EAAcvU,EACrEwU,EAAqBhW,KAAK3F,GAI1BsD,GAAgBoY,EAAc,EACXE,GAAdF,IACDE,GAAiBF,EACjBL,EAAiBrb,EACjB6b,GAAwB,GAKdD,EAAdF,IACAE,EAAgBF,EAChBL,EAAiBrb,EACjB6b,GAAwB,GAIhC,GAA0B,EAAtBhD,EAGA,IADA,IAAIhZ,EAAI,EACkB,IAAnBwb,GAAsD,EAA9BM,EAAqBpb,QAAcV,EAAIgZ,GAAqB,CACvF,IAAIiD,EAAwB,GACxB1K,EAAe/J,KAAKwJ,mBAAmBhR,GAAGuR,aAE9CwK,EAAgBzU,EAEhB,IAAK,IAAIrH,EAAI,EAAGA,EAAI6b,EAAqBpb,OAAQT,IAG7C4b,EAActK,EAFdpR,EAAI2b,EAAqB7b,IAGzBwD,GAAgE,IAAjD+D,KAAKmJ,iBAAiBnJ,KAAKqJ,cAAc1Q,KAEnDmH,EAAYuU,GAAeA,EAAcvU,EAC1C2U,EAAsBnW,KAAK3F,GAI3BsD,GAAgBoY,EAAc,EACXE,GAAdF,IACDE,GAAiBF,EACjBL,EAAiBrb,EACjB6b,GAAwB,GAKdD,EAAdF,IACAE,EAAgBF,EAChBL,EAAiBrb,EACjB6b,GAAwB,GAGhCF,EAAuBG,EACvBjc,GAAK,EAMb,GAAuB,IAAnBwb,EAGA,OAFAhU,KAAKwL,gBACLxL,KAAKoJ,cAAgB,EACdoE,EASX,IALA,IAAI0G,EAAa,EACbQ,EAAc5G,EAAAA,EAIT1V,GAFW4H,KAAKsI,cAEZ,GAAGlQ,GAAKqQ,EAASrQ,IAAK,CAC/B,IAAIiT,EAAM9C,EAAOnQ,GACb2b,EAAW1I,EAAI7C,GACfmM,EAAWtJ,EAAI2I,GAEnB,MAAKlU,EAAY6U,GAAYA,EAAW7U,GAAxC,CAIA,GAAe,EAAX6U,GAA4BZ,EAAZjU,IAAoCA,EAAZiU,EAAuB,CAC/DW,EAAc,EACdR,EAAa9b,EACb,MAGJ,IAAI+b,EAAWK,GAAyBT,EAAWY,EAAWZ,EAAWY,EAC1D7U,EAAXqU,GAAsCA,EAAdO,IACxBA,EAAcP,EACdD,EAAa9b,IAIrB,GAAIsc,IAAgB5G,EAAAA,EAKhB,OAHA9N,KAAK2H,YAAcmG,EAAAA,EACnB9N,KAAK4H,SAAU,EACf5H,KAAK4J,kBAAoB5J,KAAKqJ,cAAc2K,GACrCxG,EAGX,GAAGzH,EAAoB,CACnB8N,EAAgBvV,KAAK,CAAC0B,KAAKsI,cAAc4L,GAAalU,KAAKqJ,cAAc2K,KAEzE,IAAII,EAAYpU,KAAKU,eAAemT,GACpC,GAAsB,EAAnBO,EAAUlb,OAOT,OALA8G,KAAK3G,MAAMsH,SAASrC,KAAK,oBACzB0B,KAAK3G,MAAMsH,SAASrC,KAAK,UAAW8V,EAAU,IAC9CpU,KAAK3G,MAAMsH,SAASrC,KAAK,WAAY8V,EAAU,IAE/CpU,KAAK0F,UAAW,EACT8H,EAIfxN,KAAKmR,MAAM+C,EAAYF,GAAgB,GACvCxG,GAAc,IAOtB,IAAIoH,EAAiB,GAQrBtV,EAAQsB,UAAUuQ,MAAQ,SAAU0D,EAAeC,GAC/C,IAAIvM,EAASvI,KAAKuI,OAEd4L,EAAW5L,EAAOsM,GAAeC,GAEjCrM,EAAUzI,KAAK0I,OAAS,EACxBwH,EAAalQ,KAAKiJ,MAAQ,EAE1B8L,EAAoB/U,KAAKsI,cAAcuM,GACvCG,EAAqBhV,KAAKqJ,cAAcyL,GAE5C9U,KAAKsI,cAAcuM,GAAiBG,EACpChV,KAAKqJ,cAAcyL,GAAoBC,EAEvC/U,KAAKsJ,cAAc0L,GAAsBH,EACzC7U,KAAKsJ,cAAcyL,IAAsB,EAEzC/U,KAAKuJ,cAAcyL,IAAuB,EAC1ChV,KAAKuJ,cAAcwL,GAAqBD,EAMxC,IAFA,IAiBI1P,EAAa3M,EAAGwc,EAjBhB5D,EAAW9I,EAAOsM,GAClBK,EAAkB,EACbvc,EAAI,EAAGA,GAAKuX,EAAYvX,KACP,OAAhB0Y,EAAS1Y,IAAgB0Y,EAAS1Y,IAAM,MAK1C0Y,EAAS1Y,GAAK,GAJd0Y,EAAS1Y,IAAMwb,EACfS,EAAeM,GAAmBvc,EAClCuc,GAAmB,GAK3B7D,EAASyD,GAAoB,EAAIX,EAOjBnU,KAAKF,UAmBrB,IAnBA,IAmBS1H,EAAI,EAAGA,GAAKqQ,EAASrQ,IAC1B,GAAIA,IAAMyc,MAE+B,OAAhCtM,EAAOnQ,GAAG0c,IAA+BvM,EAAOnQ,GAAG0c,IAAqB,OAAO,CAIhF,IAAIzJ,EAAM9C,EAAOnQ,GAOjB,IAAsB,QAJtBgN,EAAciG,EAAIyJ,KAIa1P,GAAe,MAiBvB,IAAhBA,IACCiG,EAAIyJ,GAAoB,OAlBsB,CAClD,IAAKrc,EAAI,EAAGA,EAAIyc,EAAiBzc,KAKhB,QADbwc,EAAK5D,EAHL1Y,EAAIic,EAAenc,MAIGwc,GAAM,MAGd,IAAPA,IACC5D,EAAS1Y,GAAK,GAHlB0S,EAAI1S,GAAK0S,EAAI1S,GAAKyM,EAAc6P,EAQxC5J,EAAIyJ,IAAqB1P,EAAc+O,GAWvD,IAAI3C,EAAsBxR,KAAKwJ,mBAAmBtQ,OAClD,GAA0B,EAAtBsY,EACA,IAAK,IAAIhZ,EAAI,EAAGA,EAAIgZ,EAAqBhZ,GAAK,EAAG,CAC7C,IAAIuR,EAAe/J,KAAKwJ,mBAAmBhR,GAAGuR,aAE9C,GAAoB,KADpB3E,EAAc2E,EAAa+K,IACJ,CACnB,IAAKrc,EAAI,EAAGA,EAAIyc,EAAiBzc,IAGlB,KADXwc,EAAK5D,EADL1Y,EAAIic,EAAenc,OAGfsR,EAAapR,GAAKoR,EAAapR,GAAKyM,EAAc6P,GAI1DlL,EAAa+K,IAAqB1P,EAAc+O,KAQhE7U,EAAQsB,UAAUF,eAAiB,SAAUyU,GACzC,IAAK,IAAIC,EAAK,EAAGA,EAAKD,EAAWjc,OAAS,EAAGkc,IACzC,IAAK,IAAIC,EAAKD,EAAK,EAAGC,EAAKF,EAAWjc,OAAQmc,IAAM,CAChD,IAAIC,EAAOH,EAAWC,GAClBG,EAAOJ,EAAWE,GACtB,GAAIC,EAAK,KAAOC,EAAK,IAAMD,EAAK,KAAOC,EAAK,GAAI,CAC5C,GAAIF,EAAKD,EAAKD,EAAWjc,OAASmc,EAC9B,MAGJ,IADA,IAAIG,GAAa,EACR/c,EAAI,EAAGA,EAAI4c,EAAKD,EAAI3c,IAAK,CAC9B,IAAIgd,EAAON,EAAWC,EAAG3c,GACrBid,EAAOP,EAAWE,EAAG5c,GACzB,GAAGgd,EAAK,KAAOC,EAAK,IAAMD,EAAK,KAAOC,EAAK,GAAI,CAC3CF,GAAa,EACb,OAGR,GAAIA,EACA,MAAO,CAACJ,EAAIC,EAAKD,IAKjC,MAAO,KAGT,CAAC7I,eAAe,IAAIoJ,GAAG,CAAC,SAASxd,EAAQD,EAAOD,GAelDA,EAAQ2d,yBAA2B,SAASvc,GAMxC,IAAIwc,EACAnZ,EAAGG,EAEP,GAA6B,iBAAnBxD,EAAMiB,SAAsB,CAClC,GAAGjB,EAAMkB,YAAYlB,EAAMiB,UAAU,CAKjC,IAAIoC,KAHJmZ,EAAW1O,KAAKC,SAGP/N,EAAMmB,UAERnB,EAAMmB,UAAUkC,GAAGrD,EAAMiB,YACxBjB,EAAMmB,UAAUkC,GAAGmZ,GAAYxc,EAAMmB,UAAUkC,GAAGrD,EAAMiB,WAQhE,OAFAjB,EAAMkB,YAAYsb,GAAYxc,EAAMkB,YAAYlB,EAAMiB,iBAC/CjB,EAAMkB,YAAYlB,EAAMiB,UACxBjB,EAEP,OAAOA,EAIX,IAAIwD,KAAKxD,EAAMiB,SACX,GAAGjB,EAAMkB,YAAYsC,GAIjB,GAA4B,UAAzBxD,EAAMkB,YAAYsC,UAGVxD,EAAMiB,SAASuC,OAEnB,CAKH,IAAIH,KAHJmZ,EAAW1O,KAAKC,SAGP/N,EAAMmB,UAERnB,EAAMmB,UAAUkC,GAAGG,KAClBxD,EAAMmB,UAAUkC,GAAGmZ,GAAYxc,EAAMmB,UAAUkC,GAAGG,IAK1DxD,EAAMkB,YAAYsb,GAAYxc,EAAMkB,YAAYsC,UACzCxD,EAAMkB,YAAYsC,GAIrC,OAAOxD,IAIb,IAAIyc,GAAG,CAAC,SAAS3d,EAAQD,EAAOD,GAUlC,SAASyH,EAASsC,EAAID,EAAMb,EAAOiB,GAC/BnC,KAAKgC,GAAKA,EACVhC,KAAK+B,KAAOA,EACZ/B,KAAKkB,MAAQA,EACblB,KAAK2M,MAAQ,EACb3M,KAAKmC,SAAWA,EAGpB,SAASxC,EAAgBqC,EAAID,EAAMb,EAAOiB,GACtCzC,EAASzG,KAAK+G,KAAMgC,EAAID,EAAMb,EAAOiB,GAIzC,SAAS0N,EAAc7N,EAAId,GACvBxB,EAASzG,KAAK+G,KAAMgC,EAAI,EAAGd,EAAO,GAMtC,SAAStB,EAAKwC,EAAUgD,GACpBpF,KAAKoC,SAAWA,EAChBpC,KAAKoF,YAAcA,EAGvB,SAAS2Q,EAAyB1c,EAAO0K,EAAQ5B,GAC7C,OAAiB,IAAbA,GAA+B,aAAbA,EACX,MAGX4B,EAASA,GAAU,EACnB5B,EAAWA,GAAY,GAEM,IAAzB9I,EAAMiH,iBACNyD,GAAUA,GAGP1K,EAAMyI,YAAYiC,EAAQ,IAAO1K,EAAMmH,mBAAoB,GAAO,EAAO2B,IAKpF,SAAS3C,EAAWvE,EAAKqQ,EAAcpK,EAAO7H,GAC1C2G,KAAKsB,MAAQ,IAAIuO,EAAc,IAAM3O,EAAOA,GAC5ClB,KAAKkB,MAAQA,EACblB,KAAK3G,MAAQA,EACb2G,KAAK/E,IAAMA,EACX+E,KAAKsL,aAAeA,EAEpBtL,KAAKmL,MAAQ,GACbnL,KAAKgW,gBAAkB,GAGvBhW,KAAK0C,WAAa,KA4FtB,SAASjD,EAASmC,EAAiBC,GAC/B7B,KAAK+C,WAAanB,EAClB5B,KAAKgD,WAAanB,EAClB7B,KAAK3G,MAAQuI,EAAgBvI,MAC7B2G,KAAK/E,IAAM2G,EAAgB3G,IAC3B+E,KAAK0C,WAAa,KAtItBmN,EAAcjP,UAAUiI,QALxBlJ,EAAgBiB,UAAUqB,WAAY,EA6CtCzC,EAAWoB,UAAUqE,QAAU,SAAUG,EAAahD,GAClD,IAAIC,EAAWD,EAASlB,MACpB+J,EAAOjL,KAAKgW,gBAAgB3T,GAChC,QAAa4B,IAATgH,EAEAA,EAAO,IAAIrL,EAAKwC,EAAUgD,GAC1BpF,KAAKgW,gBAAgB3T,GAAY4I,EACjCjL,KAAKmL,MAAM7M,KAAK2M,IACU,IAAtBjL,KAAKsL,eACLlG,GAAeA,GAEnBpF,KAAK3G,MAAM8J,4BAA4BnD,KAAMoC,EAAUgD,OACpD,CAGH,IAAI6Q,EAAiBhL,EAAK7F,YAAcA,EACxCpF,KAAKkW,uBAAuBD,EAAgB7T,GAGhD,OAAOpC,MAGXR,EAAWoB,UAAUuV,WAAa,SAAUlL,GAExC,OAAOjL,MAGXR,EAAWoB,UAAUwV,iBAAmB,SAAUC,GAC9C,GAAIA,IAAWrW,KAAK/E,IAAK,CACrB,IAAIiI,EAAamT,EAASrW,KAAK/E,KACL,IAAtB+E,KAAKsL,eACLpI,GAAcA,GAGlBlD,KAAK/E,IAAMob,EACXrW,KAAK3G,MAAM4J,oBAAoBjD,KAAMkD,GAGzC,OAAOlD,MAGXR,EAAWoB,UAAUsV,uBAAyB,SAAUD,EAAgB7T,GACpE,IAAIC,EAAWD,EAASlB,MACxB,IAAkB,IAAdmB,EAAJ,CAKA,IAAI4I,EAAOjL,KAAKgW,gBAAgB3T,GAChC,QAAa4B,IAATgH,EAEAjL,KAAKiF,QAAQgR,EAAgB7T,QAI7B,GAAI6T,IAAmBhL,EAAK7F,YAAa,CACrC,IAAIlC,EAAa+S,EAAiBhL,EAAK7F,aACb,IAAtBpF,KAAKsL,eACLpI,GAAcA,GAGlB+H,EAAK7F,YAAc6Q,EACnBjW,KAAK3G,MAAM8J,4BAA4BnD,KAAMoC,EAAUc,GAI/D,OAAOlD,KAtBH4C,QAAQC,KAAK,6FAyBrBrD,EAAWoB,UAAUsD,MAAQ,SAAUH,EAAQ5B,GAC3CnC,KAAK0C,WAAaqT,EAAyB/V,KAAK3G,MAAO0K,EAAQ5B,GAC/DnC,KAAKsW,OAAOtW,KAAK0C,aAGrBlD,EAAWoB,UAAU0V,OAAS,SAAUC,GACT,OAAvBA,IAKAvW,KAAKsL,aACLtL,KAAKkW,wBAAwB,EAAGK,GAEhCvW,KAAKkW,uBAAuB,EAAGK,KAcvC9W,EAASmB,UAAUkC,YAAa,EAEhCrD,EAASmB,UAAUqE,QAAU,SAAUG,EAAahD,GAGhD,OAFApC,KAAK+C,WAAWkC,QAAQG,EAAahD,GACrCpC,KAAKgD,WAAWiC,QAAQG,EAAahD,GAC9BpC,MAGXP,EAASmB,UAAUuV,WAAa,SAAUlL,GAGtC,OAFAjL,KAAK+C,WAAWoT,WAAWlL,GAC3BjL,KAAKgD,WAAWmT,WAAWlL,GACpBjL,MAGXP,EAASmB,UAAUwV,iBAAmB,SAAUnb,GAC5C+E,KAAK+C,WAAWqT,iBAAiBnb,GACjC+E,KAAKgD,WAAWoT,iBAAiBnb,GACjC+E,KAAK/E,IAAMA,GAGfwE,EAASmB,UAAUsD,MAAQ,SAAUH,EAAQ5B,GACzCnC,KAAK0C,WAAaqT,EAAyB/V,KAAK3G,MAAO0K,EAAQ5B,GAC/DnC,KAAK+C,WAAWL,WAAa1C,KAAK0C,WAClC1C,KAAK+C,WAAWuT,OAAOtW,KAAK0C,YAC5B1C,KAAKgD,WAAWN,WAAa1C,KAAK0C,WAClC1C,KAAKgD,WAAWsT,OAAOtW,KAAK0C,aAIhCxK,EAAOD,QAAU,CACbuH,WAAYA,EACZE,SAAUA,EACVC,gBAAiBA,EACjBkQ,cAAeA,EACfpQ,SAAUA,EACVG,KAAMA,IAGR,IAAI4W,GAAG,CAAC,SAASre,EAAQD,EAAOD,GAgCrB,SAATwe,IAEA,aAEAzW,KAAKH,MAAQA,EACbG,KAAKiK,aAAeA,EACpBjK,KAAKR,WAAaA,EAClBQ,KAAKN,SAAWA,EAChBM,KAAK0W,QAAUA,EACf1W,KAAKJ,KAAOA,EACZI,KAAKV,QAAUA,EACfU,KAAK2W,gBAAkB,KAEvB3W,KAAK4W,SAAWA,EAgBhB5W,KAAKiH,MAAQ,SAAU5N,EAAOyG,EAAW+W,EAAMC,GAM3C,GAAGA,EACC,IAAI,IAAI3b,KAAQ4b,EACZ1d,EAAQ0d,EAAW5b,GAAM9B,GAKjC,IAAKA,EACD,MAAM,IAAIP,MAAM,yCAOpB,GAA6B,iBAAnBO,EAAMiB,UACTqJ,OAAOC,KAAsB,EAAjBvK,EAAMiB,UACjB,OAAOnC,EAAQ,YAARA,CAAqB6H,KAAM3G,GAW1C,GAAGA,EAAMwE,SAAS,CAEd,IAAImZ,EAAUrT,OAAOC,KAAKgT,GAO1B,GANAI,EAAUvQ,KAAKE,UAAUqQ,IAMrB3d,EAAMwE,SAASwI,OACf,MAAM,IAAIvN,MAAM,kHAAoHke,GAOxI,IAAIJ,EAASvd,EAAMwE,SAASwI,QACxB,MAAM,IAAIvN,MAAM,wBAA0BO,EAAMwE,SAASwI,OAAS,qCAAuC2Q,GAG7G,OAAOJ,EAASvd,EAAMwE,SAASwI,QAAQ7I,MAAMnE,GAazCA,aAAiBwG,IAAU,IAC3BxG,EAAQ,IAAIwG,EAAMC,GAAWwD,SAASjK,IAG1C,IAAI4d,EAAW5d,EAAMmE,QAOrB,GANAwC,KAAK2W,gBAAkBtd,EACvB4d,EAAS5O,YAAc4O,EAAS7O,sBAK5ByO,EACA,OAAOI,EAKP,IAAIC,EAAQ,GA2BZ,OAxBAA,EAAMxR,SAAWuR,EAASvR,SAG1BwR,EAAMhQ,OAAS+P,EAAStP,WAExBuP,EAAMtP,QAAUqP,EAASrP,QAEtBqP,EAAS9O,SAASmG,eACjB4I,EAAM7I,YAAa,GAIvB1K,OAAOC,KAAKqT,EAAS5O,aAChB5M,QAAQ,SAAUH,GAKgB,IAA5B2b,EAAS5O,YAAY/M,KACpB4b,EAAM5b,GAAK2b,EAAS5O,YAAY/M,MAKrC4b,GAenBlX,KAAKmX,WAAahf,EAAQ,kCAsC1B6H,KAAKoX,eAAiB,SAAS/d,GAC3B,OAAOlB,EAAQ,YAARA,CAAqB6H,KAAM3G,IA/M1C,IAAIiG,EAAUnH,EAAQ,sBAClB0H,EAAQ1H,EAAQ,WAChB8R,EAAe9R,EAAQ,0BACvBoH,EAAcpH,EAAQ,oBACtB4e,EAAa5e,EAAQ,gBACrBqH,EAAaD,EAAYC,WACzBE,EAAWH,EAAYG,SACvBgX,EAAUnX,EAAYmX,QACtB9W,EAAOL,EAAYK,KACnBgX,EAAWze,EAAQ,sBA8MD,mBAAXkf,OACPA,OAAO,GAAI,WACP,OAAO,IAAIZ,IAGU,iBAAX7Y,OACdA,OAAOyI,OAAS,IAAIoQ,EACG,iBAATa,OACdA,KAAKjR,OAAS,IAAIoQ,GAGtBve,EAAOD,QAAU,IAAIwe,GAEnB,CAACc,iCAAiC,EAAEC,qBAAqB,EAAEC,UAAU,EAAEC,YAAY,EAAEC,yBAAyB,GAAGC,qBAAqB,GAAGC,eAAe,GAAGzR,mBAAmB,MAAM,GAAG,CAAC", "file": "solver.js"}