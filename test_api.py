import requests
import json

# 测试数据 - 匹配前端格式
test_data = {
    "profit_coeff": {
        "x1": 274.913,
        "x2": 427.316,
        "x3": 625.992
    },
    "constraints": {
        "石灰石": [0.55, 0.55, 0.55, 20000],
        "焦炭": [0.5, 0.6, 0.4, 15000],
        "煤粉": [0.2, 0.2, 0.2, 10000],
        "废钢": [0.15, 0.2, 0.3, 8000],
        "碳排放": [2.040695, 2.099421, 2.089447, 63000]
    }
}

# 发送POST请求
try:
    response = requests.post(
        'http://localhost:5000/solve',
        json=test_data,
        headers={'Content-Type': 'application/json'}
    )
    
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")
    
except requests.exceptions.ConnectionError:
    print("无法连接到服务器，请确保Flask应用正在运行")
except Exception as e:
    print(f"请求失败: {e}")
