import requests
import json

# 测试数据 - 匹配前端格式
test_data = {
    "profit_coeff": {
        "x1": 274.913,
        "x2": 427.316,
        "x3": 625.992
    },
    "constraints": {
        "石灰石": [0.55, 0.55, 0.55, 20000],
        "焦炭": [0.5, 0.6, 0.4, 15000],
        "煤粉": [0.2, 0.2, 0.2, 10000],
        "废钢": [0.15, 0.2, 0.3, 8000],
        "碳排放": [2.040695, 2.099421, 2.089447, 63000]
    }
}

# 发送POST请求
try:
    response = requests.post(
        'http://localhost:5000/solve',
        json=test_data,
        headers={'Content-Type': 'application/json'}
    )
    
    print(f"状态码: {response.status_code}")

    if response.status_code == 200:
        result = response.json()
        print("✅ 请求成功!")

        # 检查是否有 Infinity 值被正确处理
        print("\n检查变量数据中的 Infinity 处理:")
        for var in result.get("variable_data", []):
            print(f"  {var['变量名']}:")
            print(f"    允许增量: {var['允许增量']} (类型: {type(var['允许增量']).__name__})")
            print(f"    允许减量: {var['允许减量']} (类型: {type(var['允许减量']).__name__})")

        print("\n检查约束数据中的 Infinity 处理:")
        for constraint in result.get("constraint_data", []):
            print(f"  {constraint['约束名称']}:")
            print(f"    允许增量: {constraint['允许增量']} (类型: {type(constraint['允许增量']).__name__})")
            print(f"    允许减量: {constraint['允许减量']} (类型: {type(constraint['允许减量']).__name__})")
    else:
        print(f"❌ 请求失败: {response.text}")
    
except requests.exceptions.ConnectionError:
    print("无法连接到服务器，请确保Flask应用正在运行")
except Exception as e:
    print(f"请求失败: {e}")
