{"name": "Fertilizer", "constraints": {"W": {"equal": 40, "weight": 1, "priority": 2}, "N": {"equal": 50, "weight": 1, "priority": 1}, "P": {"equal": 20, "weight": 2, "priority": 1}, "K": {"equal": 30, "weight": 1, "priority": 1}}, "variables": {"Fertilizer A": {"N": 36.0, "P": 0.0, "K": 0.0, "W": 40}, "Fertilizer B": {"N": 15.0, "P": 15.0, "K": 15.0, "W": 40}, "Fertilizer C": {"N": 5.0, "P": 25.0, "K": 10.0, "W": 40}}, "expects": {"feasible": true, "r1": 46.66666667, "r4": 10, "Fertilizer A": 0.83333333, "Fertilizer B": 1.33333333, "result": 0}}