import requests
import json

# 测试数据 - 匹配 2.py 的前端格式
test_data = {
    "profit_coeff": {
        "c1": -274.892,
        "c2": -427.305,
        "c3": -625.971
    },
    "constraints": {
        "石灰石": [2, 3, 4, 20000],
        "焦炭": [1, 2, 3, 15000],
        "煤粉": [1, 1, 2, 10000],
        "废钢": [0.5, 1, 1.5, 8000],
        "碳排放": [3, 5, 7, 63000]
    }
}

# 发送POST请求
try:
    response = requests.post(
        'http://localhost:5000/solve',
        json=test_data,
        headers={'Content-Type': 'application/json'}
    )
    
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print("✅ 请求成功!")
        
        # 检查是否有 Infinity 值被正确处理
        print("\n检查变量数据中的 Infinity 处理:")
        for var in result.get("variable_data", []):
            print(f"  {var['变量名']}:")
            print(f"    终值: {var['终值(吨)']} 吨")
            print(f"    目标系数: {var['目标系数(元/吨)']} 元/吨")
            print(f"    允许增量: {var['允许增量(元/吨)']} (类型: {type(var['允许增量(元/吨)']).__name__})")
            print(f"    允许减量: {var['允许减量(元/吨)']} (类型: {type(var['允许减量(元/吨)']).__name__})")
        
        print("\n检查约束数据中的 Infinity 处理:")
        for constraint in result.get("constraint_data", []):
            print(f"  {constraint['约束名称']}:")
            print(f"    实际使用量: {constraint['实际使用量']}")
            print(f"    影子价格: {constraint['影子价格(元/单位)']} 元/单位")
            print(f"    允许增量: {constraint['允许增量(单位)']} (类型: {type(constraint['允许增量(单位)']).__name__})")
            print(f"    允许减量: {constraint['允许减量(单位)']} (类型: {type(constraint['允许减量(单位)']).__name__})")
    else:
        print(f"❌ 请求失败: {response.text}")
        
except requests.exceptions.ConnectionError:
    print("无法连接到服务器，请确保Flask应用正在运行")
except Exception as e:
    print(f"请求失败: {e}")
