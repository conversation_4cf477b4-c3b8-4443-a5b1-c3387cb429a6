{"name": "jsLPSolver", "version": "0.4.0", "homepage": "https://github.com/JWally/jsLPSolver", "description": "Easy to use, JSON oriented Linear Programming and Mixed Int. Programming Solver", "main": "src/solver.js", "moduleType": ["globals", "node"], "keywords": ["Linear", "Programming", "Integer", "Programming", "Solver", "Linear Programming", "Linear Optimization", "Simplex", "Mixed Integer Optimization", "Mixed Integer Programming"], "authors": ["<PERSON>"], "license": "MIT", "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests"]}