#!/usr/bin/env python3
"""
测试修复后的代码是否能正确处理前端发送的数据格式
"""

# 模拟前端发送的数据
frontend_data = {
    "profit_coeff": {
        "x1": 274.913,
        "x2": 427.316,
        "x3": 625.992
    },
    "constraints": {
        "石灰石": [0.55, 0.55, 0.55, 20000],
        "焦炭": [0.5, 0.6, 0.4, 15000],
        "煤粉": [0.2, 0.2, 0.2, 10000],
        "废钢": [0.15, 0.2, 0.3, 8000],
        "碳排放": [2.040695, 2.099421, 2.089447, 63000]
    }
}

# 测试键映射逻辑
import pulp

# 创建变量（模拟代码中的变量创建）
x1 = pulp.LpVariable("X1_普通钢铁", lowBound=0, upBound=100000)
x2 = pulp.LpVariable("X2_优质钢铁", lowBound=0, upBound=100000)
x3 = pulp.LpVariable("X3_特优钢铁", lowBound=0, upBound=100000)

# 测试映射关系
variables = {
    "x1": x1,
    "x2": x2,
    "x3": x3
}

var_to_key = {x1: "x1", x2: "x2", x3: "x3"}

profit_coeff = frontend_data["profit_coeff"]

print("测试前端键到变量的映射:")
for key, var in variables.items():
    print(f"  {key} -> {var.name}, 系数: {profit_coeff[key]}")

print("\n测试变量到前端键的反向映射:")
for var, key in var_to_key.items():
    print(f"  {var.name} -> {key}, 系数: {profit_coeff[key]}")

print("\n✅ 映射关系测试通过！前端数据格式兼容。")
