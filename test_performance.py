import requests
import json
import time

# 测试数据 - 与你的请求体相同
test_data = {
    "profit_coeff": {
        "c1": 1,
        "c2": 1,
        "c3": 1
    },
    "constraints": {
        "石灰石": [1, 1, 1, 20000],
        "焦炭": [1, 1, 1, 15000],
        "煤粉": [1, 1, 1, 10000],
        "废钢": [1, 1, 1, 8000],
        "碳排放": [1, 1, 1, 63000]
    }
}

def test_api_performance():
    print("开始性能测试...")
    
    # 测试多次请求
    times = []
    for i in range(3):
        start_time = time.time()
        
        try:
            response = requests.post(
                'http://localhost:5000/solve',
                json=test_data,
                headers={'Content-Type': 'application/json'}
            )
            
            end_time = time.time()
            request_time = end_time - start_time
            times.append(request_time)
            
            if response.status_code == 200:
                result = response.json()
                server_time = result.get('processing_time', 'N/A')
                print(f"请求 {i+1}: 客户端耗时 {request_time:.3f}s, 服务器处理时间 {server_time}s")
                
                # 检查第一次请求的结果
                if i == 0:
                    print("\n变量数据:")
                    for var in result.get("variable_data", []):
                        print(f"  {var['变量名']}: 终值={var['终值(吨)']}, 系数={var['目标系数(元/吨)']}")
                    
                    print("\n约束数据:")
                    for constraint in result.get("constraint_data", []):
                        print(f"  {constraint['约束名称']}: 使用量={constraint['实际使用量']}, 影子价格={constraint['影子价格(元/单位)']}")
            else:
                print(f"请求 {i+1} 失败: {response.status_code} - {response.text}")
                
        except requests.exceptions.ConnectionError:
            print("无法连接到服务器，请确保Flask应用正在运行")
            return
        except Exception as e:
            print(f"请求 {i+1} 出错: {e}")
    
    if times:
        avg_time = sum(times) / len(times)
        print(f"\n平均响应时间: {avg_time:.3f}s")
        print(f"最快响应时间: {min(times):.3f}s")
        print(f"最慢响应时间: {max(times):.3f}s")

if __name__ == "__main__":
    test_api_performance()
