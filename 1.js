const axios = require("axios");
const fs = require("fs");

const domain = `http://*************/`;
// const domain = `https://znt.suitanglian.com`;
const Bearer = `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiZGYxOGM1MjEtMmM4NS00YTE5LWE4MWItZjYyZTU5YzM2YWI5IiwiZXhwIjoxNzUwNDkxMTM0LCJpc3MiOiJTRUxGX0hPU1RFRCIsInN1YiI6IkNvbnNvbGUgQVBJIFBhc3Nwb3J0In0.ObC3AKEmyCSl3oLioA3GpUSbyNEsUnZF48l-ziTpf1Y`;
const headers = {
  "Content-Type": "application/json",
  Authorization: `Bearer ${Bearer}`,
};

const checkServer = () => {
  const serverList = [
    // { origin: `http://************`, fileName: `************` },
    // { origin: `http://*************`, fileName: `*************` },
    // { origin: `http://***************`, fileName: `***************` },
    // { origin: `https://znt.suitanglian.com`, fileName: `znt.suitanglian.com` },
    { ssl: "http", domain: "***********", origin: `http://***********`, fileName: `***********` },
  ];
  for (const { ssl, domain } of serverList) {
    const Bearer = fs.readFileSync(`./public/dify_authorization/${domain}.txt`, "utf-8");
    const headers = {
      "Content-Type": "application/json",
      Authorization: `Bearer ${Bearer}`,
    };
    checkDatasets(ssl, domain, headers);
  }
};

const checkDatasets = async (ssl, domain, headers) => {
  let datasets = [];
  try {
    let page = 1;
    while (true) {
      const {
        data: { data },
      } = await axios.get(`${ssl}://${domain}/console/api/datasets?page=${page}&limit=100&include_all=true`, { headers });
      if (data.length) datasets = datasets.concat(data);
      else break;
      page++;
    }
    console.log(` 该服务器已有${datasets.length}个知识库 [${domain}]`);
  } catch (e) {
    console.log(`${e}`);
  }
};

const deleteServer = () => {
  const serverList = [
    // { origin: `http://************`, fileName: `************` },
    // { origin: `http://*************`, fileName: `*************` },
    // { origin: `http://***************`, fileName: `***************` },
    // { origin: `https://znt.suitanglian.com`, fileName: `znt.suitanglian.com` },
    { ssl: "http", domain: "***********" },
  ];
  for (const { ssl, domain } of serverList) {
    const Bearer = fs.readFileSync(`./public/dify_authorization/${domain}.txt`, "utf-8");
    const headers = {
      "Content-Type": "application/json",
      Authorization: `Bearer ${Bearer}`,
    };
    deleteDatasets(ssl, domain, headers);
  }
};

const deleteDatasets = async (ssl, domain, headers) => {
  const origin = `${ssl}://${domain}`;
  try {
    while (true) {
      const url = `${origin}/console/api/datasets?page=1&limit=200&include_all=true`;
      const {
        data: { data },
      } = await axios.get(url, { headers });
      console.log(`本次查询到[${data.length}]个知识库 [${domain}]`);
      let count = 0;
      for (const row of data) {
        const { id, name, app_count } = row;
        // if (name === "") {
        //   console.log(id);
        //   await deleteTarget(id, origin, headers);
        //   count++;
        // }
        if (app_count === 0) {
          await deleteTarget(id, origin, headers);
          count++;
        }
      }
      console.log(`已删除其中[${count}]个知识库 [${domain}]`);
      console.log(`---------------------------`);
      if (count === 0) break;
    }
  } catch (e) {
    console.log(`${e}`);
  }
};

const deleteTarget = async (datasetId, origin, headers) => {
  const url = `${origin}/console/api/datasets/${datasetId}`;
  await axios.delete(url, { headers });
};

deleteServer();
// checkServer();
