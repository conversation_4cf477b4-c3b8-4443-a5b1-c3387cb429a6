{"name": "Infeasible Berlin Air Lift Problem", "optimize": "capacity", "opType": "max", "constraints": {"plane": {"max": 44}, "person": {"max": 512}, "cost": {"max": 300}, "yankees": {"equal": 0}, "brit": {"min": 13}, "yank": {"max": 25}}, "variables": {"brit": {"capacity": 20000, "plane": 1, "person": 8, "cost": 5, "yankees": -2}, "yank": {"capacity": 30000, "plane": 1, "person": 16, "cost": 9, "yankees": 1}}, "expects": {"feasible": false, "result": 0}}